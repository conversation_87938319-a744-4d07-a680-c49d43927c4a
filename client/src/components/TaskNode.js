import React from 'react';
import { Handle, Position } from 'reactflow';
import {
  Box,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
  Paper
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import LinkIcon from '@mui/icons-material/Link';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { format } from 'date-fns';
import { getStatusColor } from '../utils/statusUtils';

const TaskNode = ({ data }) => {
  const { task, onEdit, onConnect, isCreatingEdge } = data;

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return format(date, 'MMM d');
    } catch (error) {
      return '';
    }
  };

  return (
    <Box
      sx={{
        padding: 1.5, // Increased padding for better spacing
        borderRadius: 1,
        width: 230, // Increased width to match the NODE_WIDTH in DependencyGraph
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: isCreatingEdge ? 'primary.main' : 'divider',
        boxShadow: isCreatingEdge ? 4 : 1,
        '&:hover': {
          boxShadow: 3,
        },
        overflow: 'visible', // Allow content to overflow for handles
        height: '100%', // Fill the entire node height
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Source handle (output) */}
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555', width: 10, height: 10 }}
      />

      {/* Target handle (input) */}
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555', width: 10, height: 10 }}
      />

      {/* Task header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
          {task.name}
        </Typography>
        <Box>
          <Tooltip title="Create Dependency">
            <IconButton size="small" onClick={onConnect}>
              <LinkIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit Task">
            <IconButton size="small" onClick={onEdit}>
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Task type */}
      <Chip
        label={task.taskType}
        size="small"
        sx={{ mb: 1 }}
      />

      {/* Task details */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
        {/* Status */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="body2" color="text.secondary">
            Status:
          </Typography>
          <Chip
            label={task.status}
            size="small"
            color={getStatusColor(task.status)}
          />
        </Box>

        {/* Start Time */}
        {task.startTime && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              Start:
            </Typography>
            <Typography variant="body2">
              {formatDate(task.startTime)}
            </Typography>
          </Box>
        )}

        {/* Duration */}
        {task.duration && task.duration !== '00:00:00' && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              Duration:
            </Typography>
            <Typography variant="body2">
              {task.duration}
            </Typography>
          </Box>
        )}

        {/* End Time (computed) */}
        {task.startTime && task.duration && task.duration !== '00:00:00' && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              End:
            </Typography>
            <Typography variant="body2">
              {(() => {
                // Calculate end time based on start time and duration
                if (!task.startTime) return 'N/A';

                try {
                  const startTime = new Date(task.startTime);
                  const [hours, minutes, seconds] = task.duration.split(':').map(Number);

                  const endTime = new Date(startTime);
                  endTime.setHours(endTime.getHours() + hours);
                  endTime.setMinutes(endTime.getMinutes() + minutes);
                  endTime.setSeconds(endTime.getSeconds() + seconds);

                  return formatDate(endTime);
                } catch (error) {
                  return 'Invalid';
                }
              })()}
            </Typography>
          </Box>
        )}

        {/* Deadline */}
        {(task.hardDeadline || task.softDeadline) && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              Deadline:
            </Typography>
            <Typography variant="body2">
              {formatDate(task.hardDeadline || task.softDeadline)}
            </Typography>
          </Box>
        )}

        {/* Assignees */}
        {task.assignees && task.assignees.length > 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              {task.assignees.length > 1 ? 'Assignees:' : 'Assignee:'}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', justifyContent: 'flex-end' }}>
              {task.assignees.length === 1 ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar
                    sx={{
                      width: 20,
                      height: 20,
                      fontSize: '0.75rem',
                      mr: 0.5
                    }}
                  >
                    {task.assignees[0].name ? task.assignees[0].name.charAt(0) : '?'}
                  </Avatar>
                  <Typography variant="body2" noWrap sx={{ maxWidth: 100 }}>
                    {task.assignees[0].name || 'Unknown'}
                  </Typography>
                </Box>
              ) : (
                <Chip
                  size="small"
                  label={`${task.assignees.length} people`}
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
              )}
            </Box>
          </Box>
        )}
      </Box>

      {/* Subtasks */}
      {task.subtasks && task.subtasks.length > 0 && (
        <Box sx={{ mt: 1.5, pt: 1.5, borderTop: '1px dashed', borderColor: 'divider', flexGrow: 1, overflow: 'auto' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 'bold' }}>
            Subtasks ({task.subtasks.length}):
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {task.subtasks.map(subtask => (
              <Paper
                key={subtask._id}
                elevation={1}
                sx={{
                  p: 1,
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider',
                  backgroundColor: 'background.default',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0.5,
                  position: 'relative',
                  '&:hover': {
                    boxShadow: 2,
                    borderColor: 'primary.light',
                  }
                }}
              >
                {/* Subtask header */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                    {subtask.name}
                  </Typography>
                  <Tooltip title="Edit Subtask">
                    <IconButton size="small" onClick={() => onEdit(subtask)} sx={{ p: 0.25 }}>
                      <EditIcon fontSize="inherit" />
                    </IconButton>
                  </Tooltip>
                </Box>

                {/* Subtask type */}
                <Chip
                  label={subtask.taskType}
                  size="small"
                  sx={{ mb: 0.5, height: 20, fontSize: '0.65rem', alignSelf: 'flex-start' }}
                />

                {/* Subtask details */}
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.25 }}>
                  {/* Status */}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="caption" color="text.secondary">
                      Status:
                    </Typography>
                    <Chip
                      label={subtask.status}
                      size="small"
                      color={getStatusColor(subtask.status)}
                      sx={{ height: 18, fontSize: '0.6rem' }}
                    />
                  </Box>

                  {/* Time info */}
                  {subtask.startTime && (
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="caption" color="text.secondary">
                        <AccessTimeIcon sx={{ fontSize: '0.8rem', mr: 0.25, verticalAlign: 'text-bottom' }} />
                      </Typography>
                      <Typography variant="caption">
                        {formatDate(subtask.startTime)}
                        {subtask.duration && subtask.duration !== '00:00:00' && ` (${subtask.duration.split(':').slice(0, 2).join(':')})` }
                      </Typography>
                    </Box>
                  )}

                  {/* Assignees */}
                  {subtask.assignees && subtask.assignees.length > 0 && (
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      {subtask.assignees.length <= 2 ? (
                        subtask.assignees.map((assignee, index) => (
                          <Avatar
                            key={index}
                            sx={{
                              width: 16,
                              height: 16,
                              fontSize: '0.6rem',
                              ml: index > 0 ? -0.5 : 0
                            }}
                          >
                            {assignee.name ? assignee.name.charAt(0) : '?'}
                          </Avatar>
                        ))
                      ) : (
                        <Chip
                          size="small"
                          label={`${subtask.assignees.length} people`}
                          sx={{ height: 16, fontSize: '0.6rem' }}
                        />
                      )}
                    </Box>
                  )}
                </Box>
              </Paper>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default TaskNode;