{"name": "event-planner-server", "version": "1.0.0", "description": "Backend server for Event Planner application", "main": "server.js", "scripts": {"prestart": "node scripts/liquibase.js update", "start": "node server.js", "predev": "node scripts/liquibase.js update", "dev": "nodemon server.js", "dev:seed": "nodemon server.js --seed", "seed": "node scripts/seedDatabase.js", "test": "NODE_ENV=test jest", "test:resources": "jest test/resources", "liquibase:update": "node scripts/liquibase.js update", "liquibase:status": "node scripts/liquibase.js status", "liquibase:rollback": "node scripts/liquibase.js rollback", "liquibase:generate": "node scripts/liquibase.js generate", "liquibase:validate": "node scripts/liquibase.js validate", "db:init": "node scripts/initDatabase.js", "db:reset": "node scripts/initDatabase.js --force"}, "dependencies": {"bcryptjs": "2.4.3", "cookie-parser": "1.4.7", "cors": "2.8.5", "dotenv": "16.0.3", "express": "4.18.2", "express-async-handler": "1.2.0", "express-session": "1.18.1", "express-validator": "7.0.1", "i18n": "0.15.1", "jsonwebtoken": "9.0.0", "liquibase": "4.4.0", "liquibase-mongodb": "^1.0.0", "mongoose": "7.2.0", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "passport": "0.7.0", "passport-google-oauth20": "2.0.0", "stripe": "^18.0.0"}, "devDependencies": {"jest": "29.5.0", "mongodb-memory-server": "10.1.4", "nodemon": "2.0.22", "supertest": "6.3.3"}, "engines": {"node": ">=14.0.0"}}