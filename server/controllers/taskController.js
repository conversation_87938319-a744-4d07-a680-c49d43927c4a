const mongoose = require('mongoose');
const Task = require('../models/Task');
const Event = require('../models/Event');
const User = require('../models/User');
const { Supply } = require('../models/resources');
const { StatusCodes } = require('http-status-codes');

// @desc    Create a new task
// @route   POST /api/tasks
// @access  Private
const createTask = async (req, res) => {
  try {
    // Add user to req body
    req.body.createdBy = req.user.id;

    // Ensure we have an event ID
    if (!req.body.event) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Event ID is required for task creation'
      });
    }

    // If assignees are specified, process them
    if (req.body.assignees && Array.isArray(req.body.assignees) && req.body.assignees.length > 0) {
      const event = await Event.findById(req.body.event);
      if (!event) {
        return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
      }

      // Process each assignee - could be a user ID or a non-user assignee object
      const processedAssignees = [];

      for (const assignee of req.body.assignees) {
        // Check if this is a non-user assignee (object with name property)
        if (typeof assignee === 'object' && assignee !== null && assignee.name) {
          console.log(`Adding non-user assignee: ${assignee.name}`);
          // Add isNonUserAssignee flag if not already present
          if (!assignee.isNonUserAssignee) {
            assignee.isNonUserAssignee = true;
          }
          processedAssignees.push(assignee);
        }
        // Check if this is a user ID (string)
        else if (typeof assignee === 'string') {
          const user = await User.findById(assignee);
          if (!user) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              message: `Cannot assign task to non-existent user with ID: ${assignee}`
            });
          }

          // Check if the assignee is the event owner or has accepted an invitation (for logging purposes)
          const isOwner = event.owner.toString() === assignee;
          const hasAcceptedInvitation = event.invitations.some(
            invitation => (
              invitation.user.toString() === assignee &&
              invitation.status === 'Accepted'
            )
          );

          // Log whether the user has joined the event
          console.log(`Assigning task to user ${assignee} who ${isOwner || hasAcceptedInvitation ? 'has' : 'has not'} joined the event`);

          processedAssignees.push(assignee);
        }
        // If it's an object with _id property (user object)
        else if (typeof assignee === 'object' && assignee !== null && assignee._id) {
          const user = await User.findById(assignee._id);
          if (!user) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              message: `Cannot assign task to non-existent user with ID: ${assignee._id}`
            });
          }

          // Check if the assignee is the event owner or has accepted an invitation (for logging purposes)
          const isOwner = event.owner.toString() === assignee._id.toString();
          const hasAcceptedInvitation = event.invitations.some(
            invitation => (
              invitation.user.toString() === assignee._id.toString() &&
              invitation.status === 'Accepted'
            )
          );

          // Log whether the user has joined the event
          console.log(`Assigning task to user ${assignee._id} who ${isOwner || hasAcceptedInvitation ? 'has' : 'has not'} joined the event`);

          processedAssignees.push(assignee._id);
        }
      }

      // Replace the assignees array with our processed version
      req.body.assignees = processedAssignees;
    }

    // Log the incoming task data
    console.log('Creating task with data:', JSON.stringify(req.body));

    // Check if user has permission to create tasks for this event
    const event = await Event.findById(req.body.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can create tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to create tasks for this event'
      });
    }

    // Check for parentTask if provided
    if (req.body.parentTask) {
      // Verify parent task exists
      const parentTask = await Task.findById(req.body.parentTask);
      if (!parentTask) {
        return res.status(StatusCodes.NOT_FOUND).json({
          message: 'Parent task not found'
        });
      }

      // Verify parent task belongs to the same event
      if (parentTask.event.toString() !== req.body.event) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          message: 'Parent task must belong to the same event'
        });
      }
    }

    // Process subtasks directly
    let subtaskIds = [];
    if (req.body.subtasks && Array.isArray(req.body.subtasks) && req.body.subtasks.length > 0) {
      console.log('[SERVER] RECEIVED SUBTASKS FROM CLIENT:', JSON.stringify(req.body.subtasks, null, 2));
      console.log('[SERVER] Subtasks types:', req.body.subtasks.map(s => typeof s));
      console.log('[SERVER] Subtasks structure:', req.body.subtasks.map((s, i) => {
        if (typeof s === 'string') return { index: i, type: 'string', value: s };
        if (s && typeof s === 'object') {
          return {
            index: i,
            type: 'object',
            hasId: Boolean(s._id),
            id: s._id ? (typeof s._id === 'string' ? s._id : s._id.toString()) : 'none',
            keys: Object.keys(s)
          };
        }
        return { index: i, type: typeof s, value: s };
      }));

      // Filter out any invalid values and convert to strings
      subtaskIds = req.body.subtasks
        .filter((id, index) => {
          const isValid = id && (typeof id === 'string' || id._id);
          if (!isValid) {
            console.warn(`[SERVER] Subtask at index ${index} is invalid:`, id);
          }
          return isValid;
        })
        .map((id, index) => {
          const stringId = typeof id === 'string' ? id.toString() : id._id.toString();
          console.log(`[SERVER] Converted subtask ${index} to string ID: ${stringId}`);
          return stringId;
        });

      console.log('[SERVER] Filtered subtask IDs:', subtaskIds);

      // Verify all subtasks exist and belong to the same event
      for (const [index, subtaskId] of subtaskIds.entries()) {
        const subtask = await Task.findById(subtaskId);
        if (!subtask) {
          console.error(`[SERVER] Subtask ${subtaskId} at index ${index} not found in database`);
          return res.status(StatusCodes.NOT_FOUND).json({
            message: `Subtask ${subtaskId} not found`
          });
        }

        console.log(`[SERVER] Found subtask ${index}:`, {
          id: subtask._id.toString(),
          name: subtask.name,
          event: subtask.event.toString()
        });

        // Verify subtask belongs to the same event
        if (subtask.event.toString() !== req.body.event) {
          console.error(`[SERVER] Subtask ${subtaskId} belongs to event ${subtask.event.toString()}, but task belongs to event ${req.body.event}`);
          return res.status(StatusCodes.BAD_REQUEST).json({
            message: 'All subtasks must belong to the same event'
          });
        }

        // For create task, we don't need to check for circular references
        // since the task doesn't exist yet
      }

      console.log(`[SERVER] Found ${subtaskIds.length} valid subtasks out of ${req.body.subtasks.length} total`);
    } else {
      console.log('[SERVER] No subtasks received from client or not an array');
    }

    // Set the subtasks directly on the task data
    req.body.subtasks = subtaskIds;
    console.log('[SERVER] Final subtasks array for task creation:', req.body.subtasks);

    // Process supplies
    let supplyIds = [];
    if (req.body.supplies && Array.isArray(req.body.supplies) && req.body.supplies.length > 0) {
      console.log('[SERVER] RECEIVED SUPPLIES FROM CLIENT:', JSON.stringify(req.body.supplies, null, 2));

      // Filter out any invalid values and convert to strings
      supplyIds = req.body.supplies
        .filter((id, index) => {
          const isValid = id && (typeof id === 'string' || id._id);
          if (!isValid) {
            console.warn(`[SERVER] Supply at index ${index} is invalid:`, id);
          }
          return isValid;
        })
        .map((id, index) => {
          const stringId = typeof id === 'string' ? id.toString() : id._id.toString();
          console.log(`[SERVER] Converted supply ${index} to string ID: ${stringId}`);
          return stringId;
        });

      console.log('[SERVER] Filtered supply IDs:', supplyIds);

      // Verify all supplies exist and belong to the same event
      for (const [index, supplyId] of supplyIds.entries()) {
        const supply = await Supply.findById(supplyId);
        if (!supply) {
          console.error(`[SERVER] Supply ${supplyId} at index ${index} not found in database`);
          return res.status(StatusCodes.NOT_FOUND).json({
            message: `Supply ${supplyId} not found`
          });
        }

        console.log(`[SERVER] Found supply ${index}:`, {
          id: supply._id.toString(),
          name: supply.name,
          event: supply.event.toString()
        });

        // Verify supply belongs to the same event
        if (supply.event.toString() !== req.body.event) {
          console.error(`[SERVER] Supply ${supplyId} belongs to event ${supply.event.toString()}, but task belongs to event ${req.body.event}`);
          return res.status(StatusCodes.BAD_REQUEST).json({
            message: 'All supplies must belong to the same event'
          });
        }
      }

      console.log(`[SERVER] Found ${supplyIds.length} valid supplies out of ${req.body.supplies.length} total`);
    } else {
      console.log('[SERVER] No supplies received from client or not an array');
    }

    // Set the supplies directly on the task data
    req.body.supplies = supplyIds;
    console.log('[SERVER] Final supplies array for task creation:', req.body.supplies);

    // Create a clean copy of the task data
    const taskData = { ...req.body };

    // Always use MongoDB
    console.log('Creating task in MongoDB');
    const task = await Task.create(taskData);

    // Update supplies to reference this task if supplies were provided
    if (supplyIds && supplyIds.length > 0) {
      console.log('[SERVER] Updating supplies to reference the new task:', task._id);
      const Supply = require('../models/resources/Supply');

      await Promise.all(supplyIds.map(async (supplyId) => {
        try {
          const supply = await Supply.findById(supplyId);
          if (supply) {
            // Add the task to the supply's tasks array if not already present
            if (!supply.tasks.includes(task._id)) {
              supply.tasks.push(task._id);
              await supply.save();
              console.log(`[SERVER] Added task ${task._id} to supply ${supplyId}`);
            }
          }
        } catch (error) {
          console.error(`[SERVER] Error updating supply ${supplyId}:`, error);
        }
      }));
    }

    // We no longer need to update parentTask references since we're using direct subtasks

    // Populate the task with its subtasks and supplies
    const populatedTask = await Task.findById(task._id)
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .populate('parentTask', 'name')
      .populate({
        path: 'subtasks',
        populate: {
          path: 'assignees',
          // Only populate if it's a valid ObjectId (for user references)
          match: { _id: { $exists: true } },
          select: 'name email'
        }
      })
      .populate('supplies');

    // Process the populated task to ensure non-user assignees are preserved
    if (populatedTask && populatedTask.assignees) {
      // Replace any null values (failed population attempts) with the original assignee objects
      populatedTask.assignees = populatedTask.assignees.map((assignee, index) => {
        if (assignee === null && task.assignees[index] && typeof task.assignees[index] === 'object') {
          return task.assignees[index];
        }
        return assignee;
      });
    }

    console.log('Created task with subtasks:', populatedTask.subtasks ? populatedTask.subtasks.length : 0);

    console.log(`Task created in MongoDB: ${task._id}`);
    return res.status(StatusCodes.CREATED).json(populatedTask);
  } catch (error) {
    console.error('Error creating task:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Get all tasks for a user (filtered by event or assignee)
// @route   GET /api/tasks
// @access  Private
const getTasks = async (req, res) => {
  try {
    const { eventId, assignee, status } = req.query;

    // Always use MongoDB - build filter object
    const filterObj = {};

    // If eventId is provided, first check if user has access to this event
    if (eventId) {
      // Check if user has access to this event (owner or accepted invitation)
      const event = await Event.findOne({
        _id: eventId,
        $or: [
          { owner: req.user.id },
          { 'invitations': {
            $elemMatch: {
              user: req.user.id,
              status: 'Accepted'
            }
          }}
        ]
      });

      if (!event) {
        return res.status(StatusCodes.FORBIDDEN).json({
          message: 'You do not have access to this event'
        });
      }

      filterObj.event = eventId;
      console.log(`Filtering tasks for event ${eventId}`);
    } else {
      // If no eventId provided, only show tasks for events the user has access to
      // Get all events the user has access to
      const accessibleEvents = await Event.find({
        $or: [
          { owner: req.user.id },
          { 'invitations': {
            $elemMatch: {
              user: req.user.id,
              status: 'Accepted'
            }
          }}
        ]
      }).select('_id');

      // Extract event IDs
      const eventIds = accessibleEvents.map(event => event._id);

      // Only show tasks for these events
      filterObj.event = { $in: eventIds };
    }

    if (assignee) {
      filterObj.assignee = assignee;
      console.log(`Filtering tasks for assignee ${assignee}`);
    }

    if (status) {
      filterObj.status = status;
      console.log(`Filtering tasks with status ${status}`);
    }

    console.log('Fetching tasks from MongoDB with filters:', JSON.stringify(filterObj));

    // Add parentTask filter to only get top-level tasks by default
    if (!req.query.includeSubtasks) {
      filterObj.parentTask = null;
    }

    // Get tasks with populated subtasks and supplies
    const tasks = await Task.find(filterObj)
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .populate('event', 'title date')
      .populate({
        path: 'subtasks',
        populate: {
          path: 'assignees',
          // Only populate if it's a valid ObjectId (for user references)
          match: { _id: { $exists: true } },
          select: 'name email'
        }
      })
      .populate('parentTask', 'name')
      .populate('supplies')
      .sort({ softDeadline: 1 });

    // Process each task to ensure non-user assignees are preserved
    if (tasks && tasks.length > 0) {
      // Get the original tasks to access the original assignee objects
      const originalTasks = await Task.find(filterObj);

      // Create a map of task IDs to their original assignees
      const taskAssigneesMap = {};
      originalTasks.forEach(task => {
        taskAssigneesMap[task._id.toString()] = task.assignees;
      });

      // Process each task
      tasks.forEach(task => {
        if (task.assignees) {
          const originalAssignees = taskAssigneesMap[task._id.toString()];
          if (originalAssignees) {
            // Replace any null values (failed population attempts) with the original assignee objects
            task.assignees = task.assignees.map((assignee, index) => {
              if (assignee === null && originalAssignees[index] && typeof originalAssignees[index] === 'object') {
                return originalAssignees[index];
              }
              return assignee;
            });
          }
        }
      });
    }

    console.log('Tasks retrieved with subtasks:', tasks.reduce((count, task) => count + (task.subtasks ? task.subtasks.length : 0), 0));

    console.log(`Found ${tasks.length} tasks in MongoDB`);

    res.status(StatusCodes.OK).json(tasks);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Get a single task
// @route   GET /api/tasks/:id
// @access  Private
const getTaskById = async (req, res) => {
  try {
    console.log(`Fetching task with ID ${req.params.id} from MongoDB`);

    // Always use MongoDB
    const task = await Task.findById(req.params.id)
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .populate('event', 'title date owner collaborators invitations')
      .populate({
        path: 'subtasks',
        populate: {
          path: 'assignees',
          // Only populate if it's a valid ObjectId (for user references)
          match: { _id: { $exists: true } },
          select: 'name email'
        }
      })
      .populate('parentTask', 'name')
      .populate('supplies');

    // Process the task to ensure non-user assignees are preserved
    if (task && task.assignees) {
      // Get the original task to access the original assignee objects
      const originalTask = await Task.findById(req.params.id);

      if (originalTask) {
        // Replace any null values (failed population attempts) with the original assignee objects
        task.assignees = task.assignees.map((assignee, index) => {
          if (assignee === null && originalTask.assignees[index] && typeof originalTask.assignees[index] === 'object') {
            return originalTask.assignees[index];
          }
          return assignee;
        });
      }
    }

    console.log('[SERVER-GET] Task retrieved with subtasks:', task.subtasks ? task.subtasks.length : 0);

    if (task.subtasks && task.subtasks.length > 0) {
      console.log('[SERVER-GET] Subtasks details:', task.subtasks.map((subtask, index) => ({
        index,
        id: subtask._id.toString(),
        name: subtask.name,
        type: subtask.taskType,
        status: subtask.status,
        assignees: subtask.assignees ? subtask.assignees.length : 0
      })));
    }

    if (!task) {
      console.log(`Task with ID ${req.params.id} not found in MongoDB`);
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to this task's event
    const event = task.event;
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Associated event not found' });
    }

    // Check if user is owner or has accepted invitation
    const isOwner = event.owner._id.toString() === req.user.id;
    const hasAcceptedInvitation = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted'
      )
    );

    if (!isOwner && !hasAcceptedInvitation) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to view this task'
      });
    }

    console.log(`Task retrieved from MongoDB: ${task._id}`);
    return res.status(StatusCodes.OK).json(task);
  } catch (error) {
    console.error('Error getting task by ID:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Update a task
// @route   PUT /api/tasks/:id
// @access  Private
const updateTask = async (req, res) => {
  try {
    console.log(`Updating task with ID ${req.params.id} in MongoDB`);

    // Always use MongoDB
    const task = await Task.findById(req.params.id);

    if (!task) {
      console.log(`Task with ID ${req.params.id} not found in MongoDB`);
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // If assignees are being updated, process them
    if (req.body.assignees && Array.isArray(req.body.assignees) && req.body.assignees.length > 0) {
      const event = await Event.findById(task.event);
      if (!event) {
        return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
      }

      // Process each assignee - could be a user ID or a non-user assignee object
      const processedAssignees = [];

      for (const assignee of req.body.assignees) {
        // Check if this is a non-user assignee (object with name property)
        if (typeof assignee === 'object' && assignee !== null && assignee.name) {
          console.log(`Updating with non-user assignee: ${assignee.name}`);
          // Add isNonUserAssignee flag if not already present
          if (!assignee.isNonUserAssignee) {
            assignee.isNonUserAssignee = true;
          }
          processedAssignees.push(assignee);
        }
        // Check if this is a user ID (string)
        else if (typeof assignee === 'string') {
          const user = await User.findById(assignee);
          if (!user) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              message: `Cannot assign task to non-existent user with ID: ${assignee}`
            });
          }

          // Check if the assignee is the event owner or has accepted an invitation (for logging purposes)
          const isOwner = event.owner.toString() === assignee;
          const hasAcceptedInvitation = event.invitations.some(
            invitation => (
              invitation.user.toString() === assignee &&
              invitation.status === 'Accepted'
            )
          );

          // Log whether the user has joined the event
          console.log(`Updating task assignee to user ${assignee} who ${isOwner || hasAcceptedInvitation ? 'has' : 'has not'} joined the event`);

          processedAssignees.push(assignee);
        }
        // If it's an object with _id property (user object)
        else if (typeof assignee === 'object' && assignee !== null && assignee._id) {
          const user = await User.findById(assignee._id);
          if (!user) {
            return res.status(StatusCodes.BAD_REQUEST).json({
              message: `Cannot assign task to non-existent user with ID: ${assignee._id}`
            });
          }

          // Check if the assignee is the event owner or has accepted an invitation (for logging purposes)
          const isOwner = event.owner.toString() === assignee._id.toString();
          const hasAcceptedInvitation = event.invitations.some(
            invitation => (
              invitation.user.toString() === assignee._id.toString() &&
              invitation.status === 'Accepted'
            )
          );

          // Log whether the user has joined the event
          console.log(`Updating task assignee to user ${assignee._id} who ${isOwner || hasAcceptedInvitation ? 'has' : 'has not'} joined the event`);

          processedAssignees.push(assignee._id);
        }
      }

      // Replace the assignees array with our processed version
      req.body.assignees = processedAssignees;
    }

    // Check if user has access to update this task
    const event = await Event.findById(task.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can update tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to update this task'
      });
    }

    // Check for parentTask if provided
    if (req.body.parentTask) {
      // Verify parent task exists
      const parentTask = await Task.findById(req.body.parentTask);
      if (!parentTask) {
        return res.status(StatusCodes.NOT_FOUND).json({
          message: 'Parent task not found'
        });
      }

      // Verify parent task belongs to the same event
      if (parentTask.event.toString() !== task.event.toString()) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          message: 'Parent task must belong to the same event'
        });
      }

      // Prevent assigning a task as its own parent
      if (req.body.parentTask === req.params.id) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          message: 'A task cannot be its own parent'
        });
      }
    }

    // Process subtasks directly - ensure we only use string IDs
    console.log('[SERVER-UPDATE] RECEIVED SUBTASKS FROM CLIENT:', JSON.stringify(req.body.subtasks, null, 2));

    // Get valid subtask IDs from the request as strings
    let subtaskIds = [];
    if (req.body.subtasks && Array.isArray(req.body.subtasks)) {
      console.log('[SERVER-UPDATE] Subtasks types:', req.body.subtasks.map(s => typeof s));
      console.log('[SERVER-UPDATE] Subtasks structure:', req.body.subtasks.map((s, i) => {
        if (typeof s === 'string') return { index: i, type: 'string', value: s };
        if (s && typeof s === 'object') {
          return {
            index: i,
            type: 'object',
            hasId: Boolean(s._id),
            id: s._id ? (typeof s._id === 'string' ? s._id : s._id.toString()) : 'none',
            keys: Object.keys(s)
          };
        }
        return { index: i, type: typeof s, value: s };
      }));

      // Filter out any invalid values and ensure all IDs are strings
      subtaskIds = req.body.subtasks
        .filter((id, index) => {
          const isValid = id && (typeof id === 'string' || (id._id && typeof id._id === 'string'));
          if (!isValid) {
            console.warn(`[SERVER-UPDATE] Subtask at index ${index} is invalid:`, id);
          }
          return isValid;
        })
        .map((id, index) => {
          const stringId = typeof id === 'string' ? id.toString() : id._id.toString();
          console.log(`[SERVER-UPDATE] Converted subtask ${index} to string ID: ${stringId}`);
          return stringId;
        });

      console.log('[SERVER-UPDATE] New subtask IDs:', subtaskIds);

      // Verify all subtasks exist and belong to the same event
      for (const [index, subtaskId] of subtaskIds.entries()) {
        const subtask = await Task.findById(subtaskId);
        if (!subtask) {
          console.error(`[SERVER-UPDATE] Subtask ${subtaskId} at index ${index} not found in database`);
          return res.status(StatusCodes.NOT_FOUND).json({
            message: `Subtask ${subtaskId} not found`
          });
        }

        console.log(`[SERVER-UPDATE] Found subtask ${index}:`, {
          id: subtask._id.toString(),
          name: subtask.name,
          event: subtask.event.toString()
        });

        // Verify subtask belongs to the same event
        if (subtask.event.toString() !== task.event.toString()) {
          console.error(`[SERVER-UPDATE] Subtask ${subtaskId} belongs to event ${subtask.event.toString()}, but task belongs to event ${task.event.toString()}`);
          return res.status(StatusCodes.BAD_REQUEST).json({
            message: 'All subtasks must belong to the same event'
          });
        }

        // Prevent circular references
        if (subtaskId === req.params.id) {
          console.error(`[SERVER-UPDATE] Circular reference detected: task ${req.params.id} cannot be its own subtask`);
          return res.status(StatusCodes.BAD_REQUEST).json({
            message: 'A task cannot be its own subtask'
          });
        }
      }

      console.log(`[SERVER-UPDATE] Found ${subtaskIds.length} valid subtasks out of ${req.body.subtasks.length} total`);
    } else {
      console.log('[SERVER-UPDATE] No subtasks array provided, keeping existing subtasks');
      // Keep existing subtasks if no subtasks array provided
      subtaskIds = task.subtasks.map(id => id.toString());
      console.log('[SERVER-UPDATE] Keeping existing subtasks:', subtaskIds);
    }

    // Set the subtasks directly on the task data
    req.body.subtasks = subtaskIds;
    console.log('[SERVER-UPDATE] Final subtasks array for task update:', req.body.subtasks);

    // Process supplies - ensure we only use string IDs
    console.log('[SERVER-UPDATE] RECEIVED SUPPLIES FROM CLIENT:', JSON.stringify(req.body.supplies, null, 2));

    // Get valid supply IDs from the request as strings
    let supplyIds = [];
    if (req.body.supplies && Array.isArray(req.body.supplies)) {
      // Filter out any invalid values and ensure all IDs are strings
      supplyIds = req.body.supplies
        .filter((id, index) => {
          const isValid = id && (typeof id === 'string' || (id._id && typeof id._id === 'string'));
          if (!isValid) {
            console.warn(`[SERVER-UPDATE] Supply at index ${index} is invalid:`, id);
          }
          return isValid;
        })
        .map((id, index) => {
          const stringId = typeof id === 'string' ? id.toString() : id._id.toString();
          console.log(`[SERVER-UPDATE] Converted supply ${index} to string ID: ${stringId}`);
          return stringId;
        });

      console.log('[SERVER-UPDATE] New supply IDs:', supplyIds);

      // Verify all supplies exist and belong to the same event
      for (const [index, supplyId] of supplyIds.entries()) {
        const supply = await Supply.findById(supplyId);
        if (!supply) {
          console.error(`[SERVER-UPDATE] Supply ${supplyId} at index ${index} not found in database`);
          return res.status(StatusCodes.NOT_FOUND).json({
            message: `Supply ${supplyId} not found`
          });
        }

        console.log(`[SERVER-UPDATE] Found supply ${index}:`, {
          id: supply._id.toString(),
          name: supply.name,
          event: supply.event.toString()
        });

        // Verify supply belongs to the same event
        if (supply.event.toString() !== task.event.toString()) {
          console.error(`[SERVER-UPDATE] Supply ${supplyId} belongs to event ${supply.event.toString()}, but task belongs to event ${task.event.toString()}`);
          return res.status(StatusCodes.BAD_REQUEST).json({
            message: 'All supplies must belong to the same event'
          });
        }
      }

      console.log(`[SERVER-UPDATE] Found ${supplyIds.length} valid supplies out of ${req.body.supplies.length} total`);
    } else {
      console.log('[SERVER-UPDATE] No supplies array provided, keeping existing supplies');
      // Keep existing supplies if no supplies array provided
      supplyIds = task.supplies ? task.supplies.map(id => id.toString()) : [];
      console.log('[SERVER-UPDATE] Keeping existing supplies:', supplyIds);
    }

    // Set the supplies directly on the task data
    req.body.supplies = supplyIds;
    console.log('[SERVER-UPDATE] Final supplies array for task update:', req.body.supplies);

    // Create a clean copy of the task data
    const taskData = { ...req.body };

    // Get the original task to compare supplies
    const originalTask = await Task.findById(req.params.id);
    const originalSupplyIds = originalTask.supplies ? originalTask.supplies.map(id => id.toString()) : [];

    // Update the task
    await Task.findByIdAndUpdate(
      req.params.id,
      taskData,
      { new: true, runValidators: true }
    );

    // Handle supply linking changes
    if (supplyIds) {
      console.log('[SERVER-UPDATE] Updating supply-task relationships');
      const Supply = require('../models/resources/Supply');

      // Find supplies to remove from this task
      const suppliesToRemove = originalSupplyIds.filter(id => !supplyIds.includes(id));

      // Find supplies to add to this task
      const suppliesToAdd = supplyIds.filter(id => !originalSupplyIds.includes(id));

      // Remove this task from supplies that are no longer associated
      await Promise.all(suppliesToRemove.map(async (supplyId) => {
        try {
          const supply = await Supply.findById(supplyId);
          if (supply) {
            supply.tasks = supply.tasks.filter(taskId => taskId.toString() !== req.params.id);
            await supply.save();
            console.log(`[SERVER-UPDATE] Removed task ${req.params.id} from supply ${supplyId}`);
          }
        } catch (error) {
          console.error(`[SERVER-UPDATE] Error removing task from supply ${supplyId}:`, error);
        }
      }));

      // Add this task to new supplies
      await Promise.all(suppliesToAdd.map(async (supplyId) => {
        try {
          const supply = await Supply.findById(supplyId);
          if (supply && !supply.tasks.includes(req.params.id)) {
            supply.tasks.push(req.params.id);
            await supply.save();
            console.log(`[SERVER-UPDATE] Added task ${req.params.id} to supply ${supplyId}`);
          }
        } catch (error) {
          console.error(`[SERVER-UPDATE] Error adding task to supply ${supplyId}:`, error);
        }
      }));
    }

    // We no longer need to update parentTask references since we're using direct subtasks

    // Populate the updated task with all related data
    const populatedTask = await Task.findById(req.params.id)
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .populate('parentTask', 'name')
      .populate({
        path: 'subtasks',
        populate: {
          path: 'assignees',
          // Only populate if it's a valid ObjectId (for user references)
          match: { _id: { $exists: true } },
          select: 'name email'
        }
      })
      .populate('supplies');

    // Process the populated task to ensure non-user assignees are preserved
    if (populatedTask && populatedTask.assignees) {
      // Get the updated task to access the original assignee objects
      const updatedTask = await Task.findById(req.params.id);

      // Replace any null values (failed population attempts) with the original assignee objects
      populatedTask.assignees = populatedTask.assignees.map((assignee, index) => {
        if (assignee === null && updatedTask.assignees[index] && typeof updatedTask.assignees[index] === 'object') {
          return updatedTask.assignees[index];
        }
        return assignee;
      });
    }

    console.log('Updated task with subtasks:', populatedTask.subtasks ? populatedTask.subtasks.length : 0);

    console.log(`Task updated in MongoDB: ${populatedTask._id}`);
    return res.status(StatusCodes.OK).json(populatedTask);
  } catch (error) {
    console.error('Error updating task:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Delete a task
// @route   DELETE /api/tasks/:id
// @access  Private
const deleteTask = async (req, res) => {
  try {
    console.log(`Deleting task with ID ${req.params.id} from MongoDB`);

    // Always use MongoDB
    const task = await Task.findById(req.params.id);

    if (!task) {
      console.log(`Task with ID ${req.params.id} not found in MongoDB`);
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to delete this task
    const event = await Event.findById(task.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner or admin
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );

    // Only owner or admin can delete tasks
    if (!isOwner && !isAdminCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to delete this task'
      });
    }

    // We don't need to update parentTask references anymore
    // Just delete the task

    await Task.deleteOne({ _id: req.params.id });
    console.log(`Task deleted from MongoDB: ${req.params.id}`);

    return res.status(StatusCodes.OK).json({ message: 'Task removed' });
  } catch (error) {
    console.error('Error deleting task:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Delete multiple tasks
// @route   POST /api/tasks/batch-delete
// @access  Private
const batchDeleteTasks = async (req, res) => {
  try {
    console.log('Batch delete endpoint called');
    console.log('Request body:', req.body);

    const { taskIds } = req.body;

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      console.log('Invalid taskIds:', taskIds);
      return res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Task IDs must be provided as a non-empty array'
      });
    }

    console.log(`Batch deleting ${taskIds.length} tasks:`, taskIds);

    // Get all tasks to check permissions
    const tasks = await Task.find({ _id: { $in: taskIds } });
    console.log(`Found ${tasks.length} tasks out of ${taskIds.length} requested`);

    if (tasks.length === 0) {
      console.log('No tasks found with the provided IDs');
      return res.status(StatusCodes.NOT_FOUND).json({
        message: 'No tasks found with the provided IDs'
      });
    }

    // Group tasks by event to check permissions for each event
    const tasksByEvent = {};
    tasks.forEach(task => {
      const eventId = task.event.toString();
      if (!tasksByEvent[eventId]) {
        tasksByEvent[eventId] = [];
      }
      tasksByEvent[eventId].push(task._id.toString());
    });

    // Check permissions for each event
    const eventIds = Object.keys(tasksByEvent);
    const events = await Event.find({ _id: { $in: eventIds } });

    const eventsMap = {};
    events.forEach(event => {
      eventsMap[event._id.toString()] = event;
    });

    // Check permissions for each event
    const authorizedTaskIds = [];
    const unauthorizedEventIds = [];

    for (const eventId of eventIds) {
      const event = eventsMap[eventId];

      if (!event) {
        console.log(`Event with ID ${eventId} not found`);
        continue;
      }

      // Check if user is owner or admin
      const isOwner = event.owner.toString() === req.user.id;
      const isAdminCollaborator = event.invitations.some(
        invitation => (
          invitation.user.toString() === req.user.id &&
          invitation.status === 'Accepted' &&
          invitation.role === 'Admin'
        )
      );

      // Only owner or admin can delete tasks
      if (isOwner || isAdminCollaborator) {
        authorizedTaskIds.push(...tasksByEvent[eventId]);
      } else {
        unauthorizedEventIds.push(eventId);
      }
    }

    if (authorizedTaskIds.length === 0) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to delete any of the specified tasks'
      });
    }

    // Delete authorized tasks
    const deleteResult = await Task.deleteMany({ _id: { $in: authorizedTaskIds } });
    console.log(`Deleted ${deleteResult.deletedCount} tasks`);

    // Return result with information about unauthorized tasks if any
    const result = {
      message: `${deleteResult.deletedCount} tasks deleted successfully`,
      deletedCount: deleteResult.deletedCount,
      totalRequested: taskIds.length
    };

    if (unauthorizedEventIds.length > 0) {
      result.unauthorizedCount = taskIds.length - authorizedTaskIds.length;
      result.warning = 'Some tasks could not be deleted due to insufficient permissions';
    }

    return res.status(StatusCodes.OK).json(result);
  } catch (error) {
    console.error('Error batch deleting tasks:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Update task status
// @route   PATCH /api/tasks/:id/status
// @access  Private
const updateTaskStatus = async (req, res) => {
  try {
    console.log(`Updating status for task with ID ${req.params.id} in MongoDB`);
    const { status } = req.body;

    if (!status) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Please provide a status' });
    }

    const task = await Task.findById(req.params.id);

    if (!task) {
      console.log(`Task with ID ${req.params.id} not found in MongoDB`);
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to update this task
    // In a real app, you'd check if user is owner, assignee, or editor collaborator on the event

    task.status = status;
    const updatedTask = await task.save();
    console.log(`Status updated for task ${task._id} to ${status} in MongoDB`);

    res.status(StatusCodes.OK).json(updatedTask);
  } catch (error) {
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Get all attachments for a task
// @route   GET /api/tasks/:id/attachments
// @access  Private
const getTaskAttachments = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    res.status(StatusCodes.OK).json(task.attachments);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Upload attachment to a task
// @route   POST /api/tasks/:id/attachments
// @access  Private
const uploadTaskAttachment = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Please upload a file' });
    }

    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Add attachment to task
    task.attachments.push({
      filename: req.file.originalname,
      path: req.file.path,
    });

    const updatedTask = await task.save();

    res.status(StatusCodes.OK).json(updatedTask.attachments);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Delete attachment from a task
// @route   DELETE /api/tasks/:id/attachments/:attachmentId
// @access  Private
const deleteTaskAttachment = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Find and remove the attachment
    task.attachments = task.attachments.filter(
      attachment => attachment._id.toString() !== req.params.attachmentId
    );

    const updatedTask = await task.save();

    res.status(StatusCodes.OK).json(updatedTask.attachments);
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Get tasks for calendar view (with date range filtering)
// @route   GET /api/tasks/calendar
// @access  Private
const getTasksForCalendar = async (req, res) => {
  try {
    const { startDate, endDate, eventId } = req.query;

    // Build filter object
    const filterObj = {};

    // Filter by event if provided
    if (eventId) {
      // Check if user has access to this event (owner or accepted invitation)
      const event = await Event.findOne({
        _id: eventId,
        $or: [
          { owner: req.user.id },
          { 'invitations': {
            $elemMatch: {
              user: req.user.id,
              status: 'Accepted'
            }
          }}
        ]
      });

      if (!event) {
        return res.status(StatusCodes.FORBIDDEN).json({
          message: 'You do not have access to this event'
        });
      }

      filterObj.event = eventId;
    } else {
      // If no eventId provided, only show tasks for events the user has access to
      // Get all events the user has access to
      const accessibleEvents = await Event.find({
        $or: [
          { owner: req.user.id },
          { 'invitations': {
            $elemMatch: {
              user: req.user.id,
              status: 'Accepted'
            }
          }}
        ]
      }).select('_id');

      // Extract event IDs
      const eventIds = accessibleEvents.map(event => event._id);

      // Only show tasks for these events
      filterObj.event = { $in: eventIds };
    }

    // Add date range filter - look for tasks that have start times or deadlines in the range
    if (startDate && endDate) {
      filterObj.$or = [
        // Tasks with start time in the range
        {
          startTime: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        },
        // Tasks with soft deadline in the range
        {
          softDeadline: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        },
        // Tasks with hard deadline in the range
        {
          hardDeadline: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      ];
    }

    console.log('Fetching calendar tasks with filters:', JSON.stringify(filterObj));

    // Get tasks with populated references
    const tasks = await Task.find(filterObj)
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .populate('event', 'title date location');

    // Process each task to ensure non-user assignees are preserved
    if (tasks && tasks.length > 0) {
      // Get the original tasks to access the original assignee objects
      const originalTasks = await Task.find(filterObj);

      // Create a map of task IDs to their original assignees
      const taskAssigneesMap = {};
      originalTasks.forEach(task => {
        taskAssigneesMap[task._id.toString()] = task.assignees;
      });

      // Process each task
      tasks.forEach(task => {
        if (task.assignees) {
          const originalAssignees = taskAssigneesMap[task._id.toString()];
          if (originalAssignees) {
            // Replace any null values (failed population attempts) with the original assignee objects
            task.assignees = task.assignees.map((assignee, index) => {
              if (assignee === null && originalAssignees[index] && typeof originalAssignees[index] === 'object') {
                return originalAssignees[index];
              }
              return assignee;
            });
          }
        }
      });
    }

    console.log(`Found ${tasks.length} tasks for calendar view`);

    res.status(StatusCodes.OK).json(tasks);
  } catch (error) {
    console.error('Error fetching calendar tasks:', error.message);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Export tasks for an event
// @route   GET /api/tasks/export/:eventId
// @access  Private
const exportTasks = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Check if user has access to this event
    const event = await Event.findOne({
      _id: eventId,
      $or: [
        { owner: req.user.id },
        { 'invitations': {
          $elemMatch: {
            user: req.user.id,
            status: 'Accepted'
          }
        }}
      ]
    });

    if (!event) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'You do not have access to this event'
      });
    }

    // Get all tasks for the event
    const tasks = await Task.find({ event: eventId })
      .populate({
        path: 'assignees',
        // Only populate if it's a valid ObjectId (for user references)
        match: { _id: { $exists: true } },
        select: 'name email'
      })
      .sort({ softDeadline: 1 });

    // Get the original tasks to access the original assignee objects
    const originalTasks = await Task.find({ event: eventId });

    // Create a map of task IDs to their original assignees
    const taskAssigneesMap = {};
    originalTasks.forEach(task => {
      taskAssigneesMap[task._id.toString()] = task.assignees;
    });

    // Transform tasks to a more export-friendly format
    const exportTasks = tasks.map(task => {
      const taskObj = task.toObject();
      const originalAssignees = taskAssigneesMap[task._id.toString()];

      // Convert assignees to a simpler format if they exist
      if (taskObj.assignees && taskObj.assignees.length > 0) {
        // Process each assignee, handling both populated user references and non-user assignees
        taskObj.assignees = taskObj.assignees.map((assignee, index) => {
          // If this is a null value (failed population attempt), use the original assignee object
          if (assignee === null && originalAssignees && originalAssignees[index]) {
            const originalAssignee = originalAssignees[index];
            // If it's a non-user assignee object
            if (typeof originalAssignee === 'object' && originalAssignee.isNonUserAssignee) {
              return {
                name: originalAssignee.name,
                email: originalAssignee.email || '',
                isNonUserAssignee: true
              };
            }
          }

          // For regular user assignees
          return {
            name: assignee ? assignee.name : '',
            email: assignee ? assignee.email : ''
          };
        });
      }

      return taskObj;
    });

    res.status(StatusCodes.OK).json(exportTasks);
  } catch (error) {
    console.error('Error exporting tasks:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Import tasks for an event
// @route   POST /api/tasks/import/:eventId
// @access  Private
const importTasks = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { tasks } = req.body;

    if (!Array.isArray(tasks)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Tasks must be provided as an array'
      });
    }

    // Check if user has permission to create tasks for this event
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can import tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to import tasks for this event'
      });
    }

    // First pass: Create all tasks without dependencies
    const importedTasks = [];
    const taskIdMap = {}; // Map to store original task IDs to new MongoDB IDs
    const tasksWithDependencies = []; // Store tasks that have dependencies for second pass
    const errors = [];

    // First pass - create all tasks
    for (let i = 0; i < tasks.length; i++) {
      try {
        const taskData = tasks[i];
        const originalId = taskData.id || `task-${i+1}`; // Use provided ID or generate one

        // Prepare task data for import
        const newTaskData = {
          name: taskData.name,
          taskType: taskData.taskType || 'General',
          details: taskData.details,
          status: taskData.status || 'Not Started',
          event: eventId,
          createdBy: req.user.id
        };

        // Add optional fields if they exist
        if (taskData.softDeadline) newTaskData.softDeadline = taskData.softDeadline;
        if (taskData.hardDeadline) newTaskData.hardDeadline = taskData.hardDeadline;
        if (taskData.startTime) newTaskData.startTime = taskData.startTime;
        if (taskData.duration) newTaskData.duration = taskData.duration;
        if (taskData.location) newTaskData.location = taskData.location;
        if (taskData.cost) newTaskData.cost = taskData.cost;

        // Process assignees if they exist
        if (taskData.assignees && Array.isArray(taskData.assignees) && taskData.assignees.length > 0) {
          const processedAssignees = [];

          for (const assignee of taskData.assignees) {
            // If it's a string (user ID), verify it exists
            if (typeof assignee === 'string') {
              try {
                const user = await User.findById(assignee);
                if (user) {
                  processedAssignees.push(assignee);
                } else {
                  console.warn(`User with ID ${assignee} not found, skipping this assignee`);
                }
              } catch (err) {
                console.warn(`Error finding user with ID ${assignee}, skipping this assignee:`, err.message);
              }
            }
            // If it's an object with _id (user object), verify it exists
            else if (typeof assignee === 'object' && assignee !== null && assignee._id) {
              try {
                const user = await User.findById(assignee._id);
                if (user) {
                  processedAssignees.push(assignee._id);
                } else {
                  console.warn(`User with ID ${assignee._id} not found, skipping this assignee`);
                }
              } catch (err) {
                console.warn(`Error finding user with ID ${assignee._id}, skipping this assignee:`, err.message);
              }
            }
            // If it's an object with name (non-user assignee)
            else if (typeof assignee === 'object' && assignee !== null && assignee.name) {
              // Add isNonUserAssignee flag if not already present
              if (!assignee.isNonUserAssignee) {
                assignee.isNonUserAssignee = true;
              }
              processedAssignees.push(assignee);
              console.log(`Added non-user assignee: ${assignee.name}`);
            }
            // If it's just a name string
            else if (typeof assignee === 'string' && !mongoose.Types.ObjectId.isValid(assignee)) {
              const nonUserAssignee = {
                name: assignee,
                isNonUserAssignee: true
              };
              processedAssignees.push(nonUserAssignee);
              console.log(`Added non-user assignee from name string: ${assignee}`);
            }
          }

          if (processedAssignees.length > 0) {
            newTaskData.assignees = processedAssignees;
          }
        }

        // Log the task data before creation for debugging
        console.log(`Creating task with data:`, JSON.stringify({
          name: newTaskData.name,
          assignees: newTaskData.assignees
        }));

        // Create the task
        const task = await Task.create(newTaskData);

        // Log the created task for debugging
        console.log(`Created task: ${task.name} with assignees:`, JSON.stringify(task.assignees));

        importedTasks.push(task);

        // Store the mapping between original ID and MongoDB ID
        taskIdMap[originalId] = task._id;

        // If this task has dependencies, store it for the second pass
        if (taskData.dependencies && Array.isArray(taskData.dependencies) && taskData.dependencies.length > 0) {
          tasksWithDependencies.push({
            taskId: task._id,
            dependencies: taskData.dependencies
          });
        }

        // If this task has a parent task, store it for the third pass
        if (taskData.parentTask) {
          tasksWithDependencies.push({
            taskId: task._id,
            parentTask: taskData.parentTask
          });
        }
      } catch (error) {
        console.error('Error importing task:', error);
        errors.push({
          task: tasks[i].name,
          error: error.message
        });
      }
    }

    // Second pass - update tasks with dependencies and parent tasks
    for (const taskWithDeps of tasksWithDependencies) {
      try {
        const { taskId, dependencies, parentTask } = taskWithDeps;

        // Handle dependencies
        if (dependencies) {
          // Map the original dependency IDs to MongoDB IDs
          const mappedDependencies = dependencies.map(depId => {
            const mappedId = taskIdMap[depId];
            if (!mappedId) {
              console.warn(`Dependency ID ${depId} not found in imported tasks`);
            }
            return mappedId;
          }).filter(Boolean); // Remove any undefined values

          // Update the task with the mapped dependencies
          if (mappedDependencies.length > 0) {
            await Task.findByIdAndUpdate(taskId, { dependencies: mappedDependencies });
          }
        }

        // Handle parent task
        if (parentTask) {
          const mappedParentId = taskIdMap[parentTask];
          if (!mappedParentId) {
            console.warn(`Parent task ID ${parentTask} not found in imported tasks`);
          } else {
            // Check for circular references
            const task = await Task.findById(taskId);
            const parentTaskObj = await Task.findById(mappedParentId);

            if (task && parentTaskObj) {
              // Prevent self-reference
              if (taskId.toString() === mappedParentId.toString()) {
                console.warn(`Task ${taskId} cannot be its own parent`);
                errors.push({
                  task: task.name,
                  error: 'Task cannot be its own parent'
                });
              } else {
                // Update the task with the mapped parent task
                await Task.findByIdAndUpdate(taskId, { parentTask: mappedParentId });
              }
            }
          }
        }
      } catch (error) {
        console.error('Error updating task relationships:', error);
        errors.push({
          task: 'Unknown (relationship update)',
          error: `Failed to update relationships: ${error.message}`
        });
      }
    }

    // Get the imported tasks with populated data
    const populatedTasks = await Task.find({
      _id: { $in: importedTasks.map(task => task._id) }
    });

    // Process each task to ensure non-user assignees are preserved
    const processedTasks = [];
    for (const task of populatedTasks) {
      // Get the original task to access the original assignee objects
      const originalTask = importedTasks.find(t => t._id.toString() === task._id.toString());

      if (originalTask && originalTask.assignees) {
        // Create a processed task object with the original assignees
        const processedTask = task.toObject();
        processedTask.assignees = originalTask.assignees;
        processedTasks.push(processedTask);
      } else {
        processedTasks.push(task.toObject());
      }
    }

    res.status(StatusCodes.OK).json({
      success: true,
      importedCount: importedTasks.length,
      totalCount: tasks.length,
      errors: errors.length > 0 ? errors : undefined,
      tasks: processedTasks
    });
  } catch (error) {
    console.error('Error importing tasks:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Get task schema for import
// @route   GET /api/tasks/schema
// @access  Private
const getTaskSchema = async (_, res) => {
  try {
    // Return a sample task schema for import
    const schema = {
      type: 'array',
      items: {
        type: 'object',
        required: ['name', 'taskType'],
        properties: {
          name: {
            type: 'string',
            description: 'Name of the task'
          },
          taskType: {
            type: 'string',
            description: 'Type of the task (e.g., General, Booking, Communication)'
          },
          details: {
            type: 'string',
            description: 'Detailed description of the task'
          },
          softDeadline: {
            type: 'string',
            format: 'date-time',
            description: 'Soft deadline for the task (ISO date string)'
          },
          hardDeadline: {
            type: 'string',
            format: 'date-time',
            description: 'Hard deadline for the task (ISO date string)'
          },
          startTime: {
            type: 'string',
            format: 'date-time',
            description: 'Start time for the task (ISO date string)'
          },
          duration: {
            type: 'string',
            description: 'Duration of the task in format hh:mm:ss'
          },
          location: {
            type: 'string',
            description: 'Location where the task will take place'
          },
          dependencies: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of task IDs that this task depends on'
          },
          status: {
            type: 'string',
            enum: ['Not Started', 'In Progress', 'Completed', 'Delayed', 'Cancelled'],
            default: 'Not Started',
            description: 'Current status of the task'
          },
          cost: {
            type: 'object',
            properties: {
              amount: {
                type: 'number',
                description: 'Cost amount'
              },
              currency: {
                type: 'string',
                default: 'USD',
                description: 'Currency code'
              },
              isPaid: {
                type: 'boolean',
                default: false,
                description: 'Whether the cost has been paid'
              }
            }
          },
          parentTask: {
            type: 'string',
            description: 'ID of the parent task (if this is a subtask)'
          },
          assignees: {
            type: 'array',
            items: {
              oneOf: [
                {
                  type: 'string',
                  description: 'User ID for existing users'
                },
                {
                  type: 'object',
                  properties: {
                    name: {
                      type: 'string',
                      description: 'Name of the non-user assignee'
                    },
                    email: {
                      type: 'string',
                      description: 'Optional email of the non-user assignee'
                    },
                    phone: {
                      type: 'string',
                      description: 'Optional phone number of the non-user assignee'
                    },
                    notes: {
                      type: 'string',
                      description: 'Optional notes about the non-user assignee'
                    }
                  },
                  required: ['name'],
                  description: 'Non-user assignee object with at least a name'
                }
              ]
            },
            description: 'Array of assignees (can be user IDs or non-user objects with names)'
          }
        }
      }
    };

    // Sample task for reference
    const sampleTask = {
      name: 'Book Venue',
      taskType: 'Booking',
      details: 'Contact venue and confirm booking details',
      softDeadline: new Date().toISOString(),
      status: 'Not Started',
      dependencies: [], // Empty array means no dependencies
      assignees: [
        // Example of a non-user assignee (just a name)
        { name: 'John Smith', isNonUserAssignee: true }
      ],
      cost: {
        amount: 5000,
        currency: 'USD',
        isPaid: false
      }
    };

    // Sample task with dependencies
    const sampleTaskWithDependencies = {
      name: 'Finalize Menu',
      taskType: 'Planning',
      details: 'Select final menu options with the caterer',
      softDeadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks from now
      status: 'Not Started',
      dependencies: ['task-id-1', 'task-id-2'], // Example task IDs this task depends on
      assignees: [
        // Example of a non-user assignee with more details
        {
          name: 'Jane Doe',
          email: '<EMAIL>',
          phone: '************',
          notes: 'External caterer contact',
          isNonUserAssignee: true
        },
        // You can also include user IDs for existing users
        'user-id-1'
      ],
      cost: {
        amount: 0,
        currency: 'USD',
        isPaid: false
      }
    };

    // Sample subtask
    const sampleSubtask = {
      name: 'Contact Caterer',
      taskType: 'Communication',
      details: 'Call the caterer to discuss menu options',
      softDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
      status: 'Not Started',
      parentTask: 'parent-task-id', // ID of the parent task
      // Example of using just a name string for a non-user assignee
      assignees: ['Mary Johnson'],
      cost: {
        amount: 0,
        currency: 'USD',
        isPaid: false
      }
    };

    res.status(StatusCodes.OK).json({
      schema,
      sampleTask,
      sampleTaskWithDependencies,
      sampleSubtask
    });
  } catch (error) {
    console.error('Error getting task schema:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: error.message });
  }
};

// @desc    Add a budget item to a task
// @route   POST /api/tasks/:id/budget
// @access  Private
const addBudgetItem = async (req, res) => {
  try {
    console.log(`Adding budget item to task with ID ${req.params.id}`);

    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to update this task
    const event = await Event.findById(task.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can update tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to update this task'
      });
    }

    // Add the budget item
    task.budgetItems.push(req.body);
    const updatedTask = await task.save();

    console.log(`Budget item added to task ${task._id}`);
    return res.status(StatusCodes.OK).json(updatedTask.budgetItems);
  } catch (error) {
    console.error('Error adding budget item:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Update a budget item
// @route   PUT /api/tasks/:id/budget/:budgetItemId
// @access  Private
const updateBudgetItem = async (req, res) => {
  try {
    console.log(`Updating budget item ${req.params.budgetItemId} for task ${req.params.id}`);

    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to update this task
    const event = await Event.findById(task.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can update tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to update this task'
      });
    }

    // Find the budget item index
    const budgetItemIndex = task.budgetItems.findIndex(
      item => item._id.toString() === req.params.budgetItemId
    );

    if (budgetItemIndex === -1) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Budget item not found' });
    }

    // Update the budget item
    task.budgetItems[budgetItemIndex] = {
      ...task.budgetItems[budgetItemIndex].toObject(),
      ...req.body,
      updatedAt: Date.now()
    };

    const updatedTask = await task.save();

    console.log(`Budget item updated for task ${task._id}`);
    return res.status(StatusCodes.OK).json(updatedTask.budgetItems[budgetItemIndex]);
  } catch (error) {
    console.error('Error updating budget item:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Delete a budget item
// @route   DELETE /api/tasks/:id/budget/:budgetItemId
// @access  Private
const deleteBudgetItem = async (req, res) => {
  try {
    console.log(`Deleting budget item ${req.params.budgetItemId} from task ${req.params.id}`);

    const task = await Task.findById(req.params.id);

    if (!task) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Task not found' });
    }

    // Check if user has access to update this task
    const event = await Event.findById(task.event);
    if (!event) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Event not found' });
    }

    // Check if user is owner, admin, or editor
    const isOwner = event.owner.toString() === req.user.id;
    const isAdminCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Admin'
      )
    );
    const isEditorCollaborator = event.invitations.some(
      invitation => (
        invitation.user.toString() === req.user.id &&
        invitation.status === 'Accepted' &&
        invitation.role === 'Editor'
      )
    );

    // Only owner, admin or editor can update tasks
    if (!isOwner && !isAdminCollaborator && !isEditorCollaborator) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'Not authorized to update this task'
      });
    }

    // Remove the budget item
    task.budgetItems = task.budgetItems.filter(
      item => item._id.toString() !== req.params.budgetItemId
    );

    await task.save();

    console.log(`Budget item deleted from task ${task._id}`);
    return res.status(StatusCodes.OK).json({ message: 'Budget item removed' });
  } catch (error) {
    console.error('Error deleting budget item:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

// @desc    Get all budget items for an event
// @route   GET /api/tasks/budget/:eventId
// @access  Private
const getEventBudget = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Check if user has access to this event
    const event = await Event.findOne({
      _id: eventId,
      $or: [
        { owner: req.user.id },
        { 'invitations': {
          $elemMatch: {
            user: req.user.id,
            status: 'Accepted'
          }
        }}
      ]
    });

    if (!event) {
      return res.status(StatusCodes.FORBIDDEN).json({
        message: 'You do not have access to this event'
      });
    }

    // Get all tasks for this event
    const tasks = await Task.find({ event: eventId })
      .populate('assignees', 'name email')
      .select('name taskType budgetItems cost');

    // Format the response to include task details with budget items
    const budgetData = tasks.map(task => ({
      taskId: task._id,
      taskName: task.name,
      taskType: task.taskType,
      // Include legacy cost field for backward compatibility
      legacyCost: task.cost,
      budgetItems: task.budgetItems
    }));

    return res.status(StatusCodes.OK).json(budgetData);
  } catch (error) {
    console.error('Error getting event budget:', error.message);
    res.status(StatusCodes.BAD_REQUEST).json({ message: error.message });
  }
};

module.exports = {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  deleteTask,
  batchDeleteTasks,
  updateTaskStatus,
  getTaskAttachments,
  uploadTaskAttachment,
  deleteTaskAttachment,
  getTasksForCalendar,
  exportTasks,
  importTasks,
  getTaskSchema,
  // Budget-related endpoints
  addBudgetItem,
  updateBudgetItem,
  deleteBudgetItem,
  getEventBudget
};