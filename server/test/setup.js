const mongoose = require('mongoose');

// Set up MongoDB connection before all tests
beforeAll(async () => {
  // Force test environment and use a dedicated test database
  process.env.NODE_ENV = 'test';
  const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/event-planner-test';
  
  // Check if mongoose is already connected
  if (mongoose.connection.readyState === 0) {
    // Connect to the test database if not already connected
    await mongoose.connect(MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for tests');
  } else {
    console.log('MongoDB already connected, reusing connection');
  }
});

// Clear all collections after each test
afterEach(async () => {
  if (mongoose.connection.readyState !== 0) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
    console.log('Collections cleared after test');
  }
});

// Close MongoDB connection after all tests
afterAll(async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
    console.log('MongoDB connection closed after tests');
  }
});
