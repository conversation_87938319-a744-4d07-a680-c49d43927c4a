const ShareToken = require('../models/ShareToken');
const Event = require('../models/Event');
const crypto = require('crypto');
const Task = require('../models/Task');

// POST /api/share/generate
// Body: { date: 'YYYY-MM-DD' }
// Returns: { token }
const generateShareToken = async (req, res) => {
  try {
    const { date } = req.body;
    if (!date) return res.status(400).json({ error: 'Date required' });
    const token = crypto.randomBytes(16).toString('hex');
    await ShareToken.create({ token, date: new Date(date) });
    res.json({ token });
  } catch (err) {
    res.status(500).json({ error: 'Failed to generate token' });
  }
};

// GET /api/share/:token
// Returns: { events: [...] } for that day only
const getSharedDayData = async (req, res) => {
  try {
    const { token } = req.params;
    const share = await ShareToken.findOne({ token });
    if (!share) return res.status(404).json({ error: 'Invalid or expired token' });
    // Find all events for that date
    const start = new Date(share.date);
    start.setHours(0,0,0,0);
    const end = new Date(share.date);
    end.setHours(23,59,59,999);
    const events = await Event.find({ date: { $gte: start, $lte: end } }, '-owner -user'); // Exclude user info
    // Get all tasks for these events
    const eventIds = events.map(ev => ev._id);
    const tasks = await Task.find({ event: { $in: eventIds } }).lean();
    res.json({ events, tasks });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch shared data' });
  }
};

module.exports = { generateShareToken, getSharedDayData };
