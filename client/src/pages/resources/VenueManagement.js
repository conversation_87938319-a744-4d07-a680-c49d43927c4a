import React, { useState, useEffect, useContext, useRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Container,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Radio,
  Checkbox,
  Chip,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  Alert,
  Drawer,
  SwipeableDrawer,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Divider,
  Badge,
  Snackbar,
  useTheme,
  useMediaQuery,
  Collapse,
  Card,
  CardContent,
  CardActions,
  BottomNavigation,
  BottomNavigationAction,
  AppBar,
  Toolbar,
  Tabs,
  Tab,
  Avatar,
  ListItemAvatar,
  CircularProgress
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PlaceIcon from '@mui/icons-material/Place';
import TableRestaurantIcon from '@mui/icons-material/TableRestaurant';
import EventSeatIcon from '@mui/icons-material/EventSeat';
import SaveIcon from '@mui/icons-material/Save';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SearchIcon from '@mui/icons-material/Search';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import GroupIcon from '@mui/icons-material/Group';
import MapIcon from '@mui/icons-material/Map';
import ListIcon from '@mui/icons-material/List';
import TouchAppIcon from '@mui/icons-material/TouchApp';
import PanToolIcon from '@mui/icons-material/PanTool';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { EventContext } from '../../contexts/EventContext';
import { AuthContext } from '../../contexts/AuthContext';

// Floor Plan Preview Component - Optimized for mobile
const FloorPlanPreview = ({ venue, guestList = [] }) => {
  const { t } = useTranslation();
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    if (!venue || !venue._id || !venue?.floorPlan) return;

    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    // Auto-scale for mobile
    if (isMobile && container) {
      const containerWidth = container.clientWidth - 16; // Account for padding
      const canvasWidth = venue.floorPlan.width || 800;
      const newScale = Math.min(1, containerWidth / canvasWidth);
      setScale(newScale);
    }

    const ctx = canvas.getContext('2d');
    const { width, height, elements, background } = venue.floorPlan;

    canvas.width = width || 800;
    canvas.height = height || 600;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (background) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        drawElements(ctx, elements, guestList);
      };
      img.src = background;
    } else {
      drawGrid(ctx, canvas.width, canvas.height);
      drawElements(ctx, elements, guestList);
    }
  }, [venue, guestList, isMobile]);

  const drawGrid = (ctx, width, height) => {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;

    for (let x = 0; x <= width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let y = 0; y <= height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawElements = (ctx, elements, guestList) => {
    if (!elements) return;

    elements.forEach(element => {
      let fillColor = '#ffcc80';
      const hasAssignedGuests = element.assignedGuests && element.assignedGuests.length > 0;

      if (element.type === 'seat') {
        fillColor = hasAssignedGuests ? '#81d4fa' : '#81c784';
      } else if (element.type === 'table' && hasAssignedGuests) {
        fillColor = '#b39ddb';
      }

      ctx.fillStyle = fillColor;
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 1;

      if (element.type === 'table') {
        ctx.fillRect(element.x, element.y, element.width, element.height);
        ctx.strokeRect(element.x, element.y, element.width, element.height);

        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';

        const assignedCount = element.assignedGuests?.length || 0;
        const displayText = `${t('venue.floorPlanEditor.table', 'Table')} ${element.label || element.id}${assignedCount > 0 ? ` (${assignedCount})` : ''}`;

        ctx.fillText(
          displayText,
          element.x + element.width / 2 - (displayText.length * 3),
          element.y + element.height / 2 - 5
        );

        if (assignedCount > 0) {
          const guestNames = element.assignedGuests
            .map(guestId => guestList.find(g => g._id === guestId)?.name || t('venue.floorPlanEditor.guest', 'Guest'))
            .join(', ');

          const truncatedNames = guestNames.length > 20 ? guestNames.substring(0, 18) + '...' : guestNames;

          ctx.font = '9px Arial';
          ctx.fillText(
            truncatedNames,
            element.x + element.width / 2 - (truncatedNames.length * 2),
            element.y + element.height / 2 + 10
          );
        }
      } else if (element.type === 'seat') {
        ctx.beginPath();
        ctx.arc(
          element.x + element.width / 2,
          element.y + element.height / 2,
          element.width / 2,
          0,
          2 * Math.PI
        );
        ctx.fill();
        ctx.stroke();

        ctx.fillStyle = '#000';
        ctx.font = '10px Arial';

        let displayText = element.label || element.id;

        if (hasAssignedGuests) {
          const guestId = element.assignedGuests[0];
          const guestName = guestList.find(g => g._id === guestId)?.name;
          if (guestName) {
            displayText = guestName.substring(0, 10);
          } else {
            displayText = 'Assigned';
          }
        }

        ctx.fillText(
          displayText,
          element.x + element.width / 2 - (displayText.length * 2.5),
          element.y + element.height / 2 + 3
        );
      }
    });
  };

  return (
    <Box 
      ref={containerRef}
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        justifyContent: isMobile ? 'center' : 'flex-start',
        alignItems: isMobile ? 'center' : 'flex-start',
        overflow: 'auto',
        p: 1,
        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          display: 'block',
          border: '1px solid #ccc',
          backgroundColor: '#fafafa',
          transform: isMobile ? `scale(${scale})` : 'none',
          transformOrigin: 'top left',
          maxWidth: isMobile ? '100%' : 'none',
        }}
      />
    </Box>
  );
};

// Mobile-optimized Guest Assignment Selector
const GuestAssignmentSelector = React.forwardRef(({ guests, onAssign, currentAssignment, isMultiSelect = false, currentAssignments = [], isMobile }, ref) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGuestId, setSelectedGuestId] = useState(currentAssignment || '');
  const [selectedGuestIds, setSelectedGuestIds] = useState(Array.isArray(currentAssignments) ? [...currentAssignments] : []);

  const filteredGuests = guests.filter(guest =>
    guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    guest.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAssign = () => {
    if (isMultiSelect) {
      onAssign(selectedGuestIds);
    } else {
      onAssign(selectedGuestId);
    }
  };

  const handleSelectGuest = (guestId) => {
    if (isMultiSelect) {
      setSelectedGuestIds(prev => {
        if (prev.includes(guestId)) {
          return prev.filter(id => id !== guestId);
        } else {
          return [...prev, guestId];
        }
      });
    } else {
      setSelectedGuestId(guestId);
    }
  };

  const handleRemoveAll = () => {
    if (isMultiSelect) {
      setSelectedGuestIds([]);
      onAssign([]);
    } else {
      setSelectedGuestId('');
      onAssign(null);
    }
  };

  useImperativeHandle(ref, () => ({
    handleAssign,
    getSelectedGuestId: () => selectedGuestId,
    getSelectedGuestIds: () => selectedGuestIds,
    clearSelection: () => {
      if (isMultiSelect) {
        setSelectedGuestIds([]);
      } else {
        setSelectedGuestId('');
      }
    }
  }));

  return (
    <Box>
      <TextField
        fullWidth
        variant="outlined"
        placeholder={t('venue.floorPlanEditor.searchGuests', 'Search guests...')}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 2 }}
        size={isMobile ? "small" : "medium"}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {isMobile ? (
        // Mobile-optimized list view
        <List sx={{ maxHeight: 300, overflow: 'auto', mb: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          {filteredGuests.length > 0 ? (
            filteredGuests.map((guest) => {
              const isSelected = isMultiSelect
                ? selectedGuestIds.includes(guest._id)
                : guest._id === selectedGuestId;

              return (
                <ListItem
                  key={guest._id}
                  disablePadding
                  secondaryAction={
                    isMultiSelect ? (
                      <Checkbox
                        edge="end"
                        checked={isSelected}
                        onChange={() => handleSelectGuest(guest._id)}
                      />
                    ) : (
                      <Radio
                        edge="end"
                        checked={isSelected}
                        onChange={() => handleSelectGuest(guest._id)}
                      />
                    )
                  }
                >
                  <ListItemButton 
                    onClick={() => handleSelectGuest(guest._id)}
                    selected={isSelected}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: isSelected ? 'primary.main' : 'grey.400' }}>
                        {guest.name.charAt(0).toUpperCase()}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={guest.name}
                      secondary={
                        <>
                          {guest.email}
                          <Chip
                            label={guest.rsvpStatus}
                            color={
                              guest.rsvpStatus === 'Confirmed' ? 'success' :
                              guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                            }
                            size="small"
                            sx={{ ml: 1, height: 18 }}
                          />
                        </>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              );
            })
          ) : (
            <ListItem>
              <ListItemText
                primary={t('guests.noGuests', 'No guests found')}
                sx={{ textAlign: 'center' }}
              />
            </ListItem>
          )}
        </List>
      ) : (
        // Desktop table view
        <TableContainer component={Paper} variant="outlined" sx={{ mb: 2, maxHeight: 300 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox"></TableCell>
                <TableCell>{t('guests.columns.name', 'Name')}</TableCell>
                <TableCell>{t('guests.columns.email', 'Email')}</TableCell>
                <TableCell>{t('guests.columns.rsvpStatus', 'RSVP Status')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredGuests.length > 0 ? (
                filteredGuests.map((guest) => {
                  const isSelected = isMultiSelect
                    ? selectedGuestIds.includes(guest._id)
                    : guest._id === selectedGuestId;

                  return (
                    <TableRow
                      key={guest._id}
                      hover
                      selected={isSelected}
                      onClick={() => handleSelectGuest(guest._id)}
                    >
                      <TableCell padding="checkbox">
                        {isMultiSelect ? (
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleSelectGuest(guest._id)}
                          />
                        ) : (
                          <Radio
                            checked={isSelected}
                            onChange={() => handleSelectGuest(guest._id)}
                          />
                        )}
                      </TableCell>
                      <TableCell>{guest.name}</TableCell>
                      <TableCell>{guest.email}</TableCell>
                      <TableCell>
                        <Chip
                          label={guest.rsvpStatus}
                          color={
                            guest.rsvpStatus === 'Confirmed' ? 'success' :
                            guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                          }
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography variant="body2" sx={{ py: 2 }}>
                      {t('guests.noGuests', 'No guests found')}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {isMultiSelect && selectedGuestIds.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {t('venue.floorPlanEditor.guests', 'Guests')} ({selectedGuestIds.length}):
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {selectedGuestIds.map(guestId => {
              const guest = guests.find(g => g._id === guestId);
              return guest ? (
                <Chip
                  key={guestId}
                  label={guest.name}
                  onDelete={() => handleSelectGuest(guestId)}
                  color="primary"
                  variant="outlined"
                  size="small"
                />
              ) : null;
            })}
          </Box>
        </Box>
      )}

      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        flexDirection: isMobile ? 'column' : 'row',
        gap: isMobile ? 1 : 0
      }}>
        <Button
          variant="outlined"
          color="error"
          onClick={handleRemoveAll}
          disabled={isMultiSelect ? selectedGuestIds.length === 0 : !currentAssignment}
          fullWidth={isMobile}
          size={isMobile ? "small" : "medium"}
        >
          {isMultiSelect ? t('venue.floorPlanEditor.removeAll', 'Remove All') : t('venue.floorPlanEditor.removeAssignment', 'Remove Assignment')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleAssign}
          disabled={isMultiSelect ? selectedGuestIds.length === 0 : !selectedGuestId}
          fullWidth={isMobile}
          size={isMobile ? "small" : "medium"}
        >
          {isMultiSelect ? t('venue.floorPlanEditor.assignGuests', 'Assign Guests') : t('venue.floorPlanEditor.assignGuest', 'Assign Guest')}
        </Button>
      </Box>
    </Box>
  );
});

// Mobile-optimized Venue List Component
const VenueList = ({ venues, selectedVenue, onVenueSelect, onAddVenue, onEditVenue, onDeleteVenue, t, isMobile, loading = false }) => {
  if (isMobile) {
    return (
      <Box sx={{ width: '100%', pb: 7 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">{t('venue.venues', 'Venues')}</Typography>
        </Box>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 120 }}>
            <CircularProgress size={32} />
          </Box>
        ) : venues.length === 0 ? (
          <Box sx={{ textAlign: 'center', color: 'text.secondary', py: 4 }}>
            <PlaceIcon sx={{ fontSize: 48, mb: 1, color: 'grey.400' }} />
            <Typography variant="body1">{t('venue.noVenues', 'No venues found')}</Typography>
          </Box>
        ) : (
          <List sx={{ width: '100%' }}>
            {venues.map((venue) => (
              <Card 
                key={venue._id} 
                sx={{ 
                  mb: 1,
                  border: selectedVenue?._id === venue._id ? '2px solid' : '1px solid',
                  borderColor: selectedVenue?._id === venue._id ? 'primary.main' : 'divider',
                  boxShadow: selectedVenue?._id === venue._id ? 3 : 0,
                  transition: 'box-shadow 0.2s',
                }}
              >
                <CardContent 
                  sx={{ pb: 1, cursor: 'pointer' }}
                  onClick={() => onVenueSelect(venue)}
                >
                  <Typography variant="subtitle1" fontWeight={selectedVenue?._id === venue._id ? 'bold' : 'normal'}>
                    {venue.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <LocationOnIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                    {venue.address}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <GroupIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                    {venue.capacity} guests
                  </Typography>
                </CardContent>
                <CardActions sx={{ pt: 0 }}>
                  <IconButton
                    size="small"
                    aria-label={t('venue.editVenue', 'Edit venue')}
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditVenue(venue);
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    aria-label={t('venue.deleteVenue', 'Delete venue')}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteVenue(venue._id);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </CardActions>
              </Card>
            ))}
          </List>
        )}
        <Fab
          color="primary"
          size="medium"
          aria-label={t('venue.addVenue', 'Add Venue')}
          onClick={onAddVenue}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1201,
            boxShadow: 4
          }}
        >
          <AddIcon />
        </Fab>
      </Box>
    );
  }

  // Desktop version
  return (
    <Paper
      variant="outlined"
      sx={{
        width: { md: '25%', lg: '20%', xl: '18%' },
        minWidth: { md: 280, lg: 320 },
        maxWidth: { md: 400 },
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        height: '100%',
      }}
    >
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">{t('venue.venues', 'Venues')}</Typography>
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<AddIcon />}
          onClick={onAddVenue}
          aria-label={t('venue.addVenue', 'Add Venue')}
        >
          {t('venue.addVenue', 'Add Venue')}
        </Button>
      </Box>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 120 }}>
          <CircularProgress size={32} />
        </Box>
      ) : venues.length === 0 ? (
        <Box sx={{ textAlign: 'center', color: 'text.secondary', py: 4 }}>
          <PlaceIcon sx={{ fontSize: 48, mb: 1, color: 'grey.400' }} />
          <Typography variant="body1">{t('venue.noVenues', 'No venues found')}</Typography>
        </Box>
      ) : (
        <List sx={{ flexGrow: 1, overflow: 'auto' }}>
          {venues.map((venue) => (
            <ListItemButton
              key={venue._id}
              selected={selectedVenue?._id === venue._id}
              onClick={() => onVenueSelect(venue)}
              sx={{
                mb: 1,
                borderRadius: 1,
                border: selectedVenue?._id === venue._id ? '2px solid' : '1px solid',
                borderColor: selectedVenue?._id === venue._id ? 'primary.main' : 'divider',
                boxShadow: selectedVenue?._id === venue._id ? 3 : 0,
                transition: 'box-shadow 0.2s',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <ListItemText
                primary={venue.name}
                secondary={`${venue.address} • ${venue.capacity} guests`}
                primaryTypographyProps={{
                  variant: 'subtitle2',
                  fontWeight: selectedVenue?._id === venue._id ? 'bold' : 'normal',
                }}
                secondaryTypographyProps={{
                  variant: 'caption',
                  color: 'text.secondary',
                }}
              />
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                <IconButton
                  size="small"
                  aria-label={t('venue.editVenue', 'Edit venue')}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditVenue(venue);
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  color="error"
                  aria-label={t('venue.deleteVenue', 'Delete venue')}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteVenue(venue._id);
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            </ListItemButton>
          ))}
        </List>
      )}
    </Paper>
  );
};

// Floor plan utility functions
function createFloorPlanElement(elementType, x, y) {
  const baseElement = {
    id: Date.now() + Math.random(),
    type: elementType,
    x: x,
    y: y,
    assignedGuests: []
  };

  if (elementType === 'table') {
    return {
      ...baseElement,
      width: 80,
      height: 80,
      capacity: 6,
      label: `Table ${Date.now() % 1000}`
    };
  } else if (elementType === 'seat') {
    return {
      ...baseElement,
      width: 30,
      height: 30,
      capacity: 1,
      label: `Seat ${Date.now() % 1000}`
    };
  } else {
    return {
      ...baseElement,
      width: 50,
      height: 50,
      label: 'Other'
    };
  }
}

const validateGuestAssignments = (elements, guests) => {
  return elements.map(el => {
    const elementCopy = { ...el };
    const currentGuests = Array.isArray(elementCopy.assignedGuests) ? [...elementCopy.assignedGuests] : [];

    if (currentGuests.length > 0) {
      const validGuests = currentGuests.filter(guestId =>
        guests.some(g => g._id === guestId)
      );
      elementCopy.assignedGuests = validGuests;
    } else {
      elementCopy.assignedGuests = [];
    }

    if ('assignedGuest' in elementCopy) {
      delete elementCopy.assignedGuest;
    }

    return elementCopy;
  });
};

// Mobile-optimized Floor Plan Designer
const FloorPlanDesigner = ({ venue, onSave, guestList = [], isMobile, isTablet, isLargeScreen = false, isUltraWide = false }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const guestSelectorRef = useRef(null);

  // State management
  const [elements, setElements] = useState(venue?.floorPlan?.elements || []);
  const [selectedElement, setSelectedElement] = useState(null);
  const [draggedElementType, setDraggedElementType] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [backgroundImageUrl, setBackgroundImageUrl] = useState(venue?.floorPlan?.background || null);
  const [openGuestDialog, setOpenGuestDialog] = useState(false);
  
  // Mobile-specific state
  const [activeMode, setActiveMode] = useState('select'); // 'select', 'add-table', 'add-seat', 'assign-guest'
  const [showToolbar, setShowToolbar] = useState(true);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [mobileTab, setMobileTab] = useState(0); // 0: Canvas, 1: Elements, 2: Guests
  
  // Dynamic canvas sizing
  const getOptimalCanvasSize = () => {
    if (isMobile) return { width: window.innerWidth - 32, height: 400 };
    if (isUltraWide) return { width: 1200, height: 900 };
    if (isLargeScreen) return { width: 1000, height: 750 };
    if (isTablet) return { width: 700, height: 500 };
    return { width: 900, height: 700 };
  };

  const optimalSize = getOptimalCanvasSize();
  const [canvasWidth, setCanvasWidth] = useState(optimalSize.width);
  const [canvasHeight, setCanvasHeight] = useState(optimalSize.height);
  const [canvasReady, setCanvasReady] = useState(false);
  const [isTableAssignment, setIsTableAssignment] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState('');
  const [draggedGuest, setDraggedGuest] = useState(null);
  const [isDroppingGuest, setIsDroppingGuest] = useState(false);
  const [dropTargetElement, setDropTargetElement] = useState(null);

  // Menu and history state
  const [anchorEl, setAnchorEl] = useState(null);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Dragging and resizing state
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [elementStart, setElementStart] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null);
  const [elementStartDimensions, setElementStartDimensions] = useState({ width: 0, height: 0 });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Touch support state
  const [isTouchDragging, setIsTouchDragging] = useState(false);
  const [touchStart, setTouchStart] = useState({ x: 0, y: 0 });
  const [touchElementStart, setTouchElementStart] = useState({ x: 0, y: 0 });
  const [touchResizeHandle, setTouchResizeHandle] = useState(null);
  const [pendingAddType, setPendingAddType] = useState(null);
  const [pendingGuest, setPendingGuest] = useState(null);

  // Filtered guests
  const filteredGuests = guestList.filter(guest =>
    guest.name.toLowerCase().includes(guestSearchTerm.toLowerCase()) ||
    guest.email.toLowerCase().includes(guestSearchTerm.toLowerCase())
  );

  // Initialize elements from venue data
  useEffect(() => {
    if (!venue || !venue._id) return;

    if (venue?.floorPlan?.elements) {
      const processedElements = venue.floorPlan.elements.map(element => {
        if (element.type === 'table' && !element.assignedGuests) {
          return { ...element, assignedGuests: [] };
        }
        return element;
      });

      setElements(processedElements);
      setHistory([[...processedElements]]);
      setHistoryIndex(0);
    }
    if (venue?.floorPlan?.background) {
      setBackgroundImageUrl(venue.floorPlan.background);
    }
    
    // Optimize canvas size for mobile
    const size = getOptimalCanvasSize();
    if (venue?.floorPlan?.width && venue?.floorPlan?.height && !isMobile) {
      setCanvasWidth(venue.floorPlan.width);
      setCanvasHeight(venue.floorPlan.height);
    } else {
      setCanvasWidth(size.width);
      setCanvasHeight(size.height);
    }

    setTimeout(() => {
      setCanvasReady(true);
    }, 100);
  }, [venue, isMobile]);

  // Canvas drawing effect
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !canvasReady) return;

    const ctx = canvas.getContext('2d');

    ctx.fillStyle = '#fafafa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.save();
    ctx.scale(zoom, zoom);

    if (backgroundImageUrl) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width / zoom, canvas.height / zoom);
        drawGrid(ctx, canvas.width / zoom, canvas.height / zoom);
        drawElements(ctx);
        ctx.restore();
      };
      img.src = backgroundImageUrl;
    } else {
      drawGrid(ctx, canvas.width / zoom, canvas.height / zoom);
      drawElements(ctx);
      ctx.restore();
    }
  }, [elements, selectedElement, zoom, backgroundImageUrl, canvasReady, isDragging]);

  // Canvas initialization
  useEffect(() => {
    if (canvasRef.current) {
      canvasRef.current.width = canvasWidth;
      canvasRef.current.height = canvasHeight;

      const ctx = canvasRef.current.getContext('2d');
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      setTimeout(() => {
        setCanvasReady(true);
      }, 100);
    }

    const handleResize = () => {
      if (isMobile) {
        const size = getOptimalCanvasSize();
        setCanvasWidth(size.width);
        setCanvasHeight(size.height);
      }
      setCanvasReady(false);
      setTimeout(() => {
        setCanvasReady(true);
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [canvasWidth, canvasHeight, isMobile]);

  const drawElements = (ctx) => {
    elements.forEach(element => {
      drawElement(ctx, element);

      if (selectedElement && element.id === selectedElement.id) {
        ctx.strokeStyle = '#2196f3';
        ctx.lineWidth = 2;
        ctx.strokeRect(
          element.x - 5,
          element.y - 5,
          element.width + 10,
          element.height + 10
        );
        if (!isMobile) {
          drawResizeHandles(ctx, element);
        }
      }

      if (isDroppingGuest && dropTargetElement && element.id === dropTargetElement.id) {
        ctx.strokeStyle = '#4caf50';
        ctx.lineWidth = 3;
        ctx.setLineDash([5, 3]);
        ctx.strokeRect(
          element.x - 3,
          element.y - 3,
          element.width + 6,
          element.height + 6
        );
        ctx.setLineDash([]);
      }
    });
  };

  const drawResizeHandles = (ctx, element) => {
    const handleSize = 8;
    ctx.fillStyle = '#2196f3';

    ctx.fillRect(element.x - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x + element.width - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x + element.width - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x + element.width/2 - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x + element.width/2 - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x - handleSize/2, element.y + element.height/2 - handleSize/2, handleSize, handleSize);
    ctx.fillRect(element.x + element.width - handleSize/2, element.y + element.height/2 - handleSize/2, handleSize, handleSize);
  };

  const drawGrid = (ctx, width, height) => {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;

    for (let x = 0; x <= width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let y = 0; y <= height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawElement = (ctx, element) => {
    let fillColor = '#ffcc80';
    const hasAssignedGuests = element.assignedGuests && element.assignedGuests.length > 0;

    if (element.type === 'seat') {
      fillColor = hasAssignedGuests ? '#81d4fa' : '#81c784';
    } else if (element.type === 'table' && hasAssignedGuests) {
      fillColor = '#b39ddb';
    }

    ctx.fillStyle = fillColor;
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;

    if (element.type === 'table') {
      ctx.fillRect(element.x, element.y, element.width, element.height);
      ctx.strokeRect(element.x, element.y, element.width, element.height);

      ctx.fillStyle = '#000';
      ctx.font = isMobile ? '10px Arial' : '12px Arial';

      const assignedCount = element.assignedGuests?.length || 0;
      const displayText = `${element.label || element.id}${assignedCount > 0 ? ` (${assignedCount})` : ''}`;

      ctx.fillText(
        displayText,
        element.x + element.width / 2 - (displayText.length * 3),
        element.y + element.height / 2 - 5
      );

      if (assignedCount > 0 && !isMobile) {
        const guestNames = element.assignedGuests
          .map(guestId => guestList.find(g => g._id === guestId)?.name || 'Guest')
          .join(', ');

        const truncatedNames = guestNames.length > 20 ? guestNames.substring(0, 18) + '...' : guestNames;

        ctx.font = '9px Arial';
        ctx.fillText(
          truncatedNames,
          element.x + element.width / 2 - (truncatedNames.length * 2),
          element.y + element.height / 2 + 10
        );
      }
    } else if (element.type === 'seat') {
      ctx.beginPath();
      ctx.arc(
        element.x + element.width / 2,
        element.y + element.height / 2,
        element.width / 2,
        0,
        2 * Math.PI
      );
      ctx.fill();
      ctx.stroke();

      ctx.fillStyle = '#000';
      ctx.font = isMobile ? '8px Arial' : '10px Arial';

      let displayText = element.label || element.id;

      if (hasAssignedGuests) {
        const guestId = element.assignedGuests[0];
        const guestName = guestList.find(g => g._id === guestId)?.name;
        if (guestName) {
          displayText = isMobile ? guestName.substring(0, 5) : guestName.substring(0, 10);
        }
      }

      ctx.fillText(
        displayText,
        element.x + element.width / 2 - (displayText.length * 2.5),
        element.y + element.height / 2 + 3
      );
    }
  };

  // Event handlers
  const handleElementDragStart = (e, type) => {
    e.dataTransfer.setData('text/plain', type);
    setDraggedElementType(type);
  };

  const handleCanvasDrop = (e) => {
    e.preventDefault();

    const guestId = e.dataTransfer.getData('guest');
    if (guestId) {
      handleGuestDrop(e);
      return;
    }

    const elementType = e.dataTransfer.getData('text/plain') || draggedElementType;

    if (!elementType) return;

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    const x = (e.clientX - canvasRect.left) * scaleX / zoom;
    const y = (e.clientY - canvasRect.top) * scaleY / zoom;

    const newElement = createFloorPlanElement(elementType, x, y);

    saveToHistory();
    setElements([...elements, newElement]);
    setSelectedElement(newElement);
    setDraggedElementType(null);
  };

  const handleCanvasDragOver = (e) => {
    e.preventDefault();

    if (draggedGuest) {
      const canvas = canvasRef.current;
      const canvasRect = canvas.getBoundingClientRect();

      const scaleX = canvas.width / canvasRect.width;
      const scaleY = canvas.height / canvasRect.height;

      const x = (e.clientX - canvasRect.left) * scaleX / zoom;
      const y = (e.clientY - canvasRect.top) * scaleY / zoom;

      const targetElement = findElementAtPosition(x, y);

      setIsDroppingGuest(true);
      setDropTargetElement(targetElement);
    }
  };

  const handleGuestDragStart = (e, guest) => {
    e.dataTransfer.setData('guest', guest._id);
    setDraggedGuest(guest);
  };

  const handleGuestDrop = (e) => {
    const guestId = e.dataTransfer.getData('guest');
    if (!guestId) return;

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    const x = (e.clientX - canvasRect.left) * scaleX / zoom;
    const y = (e.clientY - canvasRect.top) * scaleY / zoom;

    const targetElement = findElementAtPosition(x, y);

    if (targetElement) {
      const updatedElements = elements.map(element => {
        if (element.id === targetElement.id) {
          if (element.type === 'seat') {
            return {
              ...element,
              assignedGuests: [guestId]
            };
          }
          else if (element.type === 'table') {
            const currentGuests = Array.isArray(element.assignedGuests) ? [...element.assignedGuests] : [];
            if (!currentGuests.includes(guestId)) {
              return {
                ...element,
                assignedGuests: [...currentGuests, guestId]
              };
            }
          }
        }
        return element;
      });

      setElements(updatedElements);

      if (selectedElement && selectedElement.id === targetElement.id) {
        const updatedElement = updatedElements.find(e => e.id === targetElement.id);
        setSelectedElement(updatedElement);
      }

      saveToHistory();
      showNotification(t('venue.floorPlanEditor.guestAssigned', 'Guest assigned successfully'));
    }

    setDraggedGuest(null);
    setIsDroppingGuest(false);
    setDropTargetElement(null);
  };

  const findElementAtPosition = (x, y) => {
    for (let i = elements.length - 1; i >= 0; i--) {
      const element = elements[i];

      if (element.type === 'table') {
        if (
          x >= element.x &&
          x <= element.x + element.width &&
          y >= element.y &&
          y <= element.y + element.height
        ) {
          return element;
        }
      } else if (element.type === 'seat') {
        const centerX = element.x + element.width / 2;
        const centerY = element.y + element.height / 2;
        const radius = element.width / 2;
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));

        if (distance <= radius) {
          return element;
        }
      }
    }

    return null;
  };

  const getResizeHandleAtPosition = (x, y) => {
    if (!selectedElement) return null;

    const handleSize = 8;
    const element = selectedElement;

    const handles = [
      { type: 'nw', x: element.x - handleSize/2, y: element.y - handleSize/2 },
      { type: 'ne', x: element.x + element.width - handleSize/2, y: element.y - handleSize/2 },
      { type: 'sw', x: element.x - handleSize/2, y: element.y + element.height - handleSize/2 },
      { type: 'se', x: element.x + element.width - handleSize/2, y: element.y + element.height - handleSize/2 },
      { type: 'n', x: element.x + element.width/2 - handleSize/2, y: element.y - handleSize/2 },
      { type: 's', x: element.x + element.width/2 - handleSize/2, y: element.y + element.height - handleSize/2 },
      { type: 'w', x: element.x - handleSize/2, y: element.y + element.height/2 - handleSize/2 },
      { type: 'e', x: element.x + element.width - handleSize/2, y: element.y + element.height/2 - handleSize/2 },
    ];

    for (const handle of handles) {
      if (
        x >= handle.x && x <= handle.x + handleSize &&
        y >= handle.y && y <= handle.y + handleSize
      ) {
        return handle.type;
      }
    }

    return null;
  };

  const getResizeCursor = (handle) => {
    const cursors = {
      'nw': 'nwse-resize',
      'ne': 'nesw-resize',
      'sw': 'nesw-resize',
      'se': 'nwse-resize',
      'n': 'ns-resize',
      's': 'ns-resize',
      'e': 'ew-resize',
      'w': 'ew-resize'
    };
    return cursors[handle] || (selectedElement ? 'grab' : 'default');
  };

  const saveToHistory = () => {
    const elementsCopy = elements.map(element => {
      if (element.type === 'table') {
        return {
          ...element,
          assignedGuests: Array.isArray(element.assignedGuests) ? [...element.assignedGuests] : []
        };
      }
      return { ...element };
    });

    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(elementsCopy);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Touch event handlers
  const getTouchPos = (touch) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    return {
      x: (touch.clientX - rect.left) * scaleX / zoom,
      y: (touch.clientY - rect.top) * scaleY / zoom,
    };
  };

  const handleTouchStart = (e) => {
    if (!canvasRef.current) return;
    if (e.touches.length !== 1) return;
    
    const touch = e.touches[0];
    const { x, y } = getTouchPos(touch);
    setMousePosition({ x, y });

    // Handle different modes for mobile
    if (activeMode === 'add-table') {
      handleAddElementAt(x, y, 'table');
      setActiveMode('select');
      showNotification(t('venue.floorPlanEditor.tableAdded', 'Table added'));
      e.preventDefault();
      return;
    }
    
    if (activeMode === 'add-seat') {
      handleAddElementAt(x, y, 'seat');
      setActiveMode('select');
      showNotification(t('venue.floorPlanEditor.seatAdded', 'Seat added'));
      e.preventDefault();
      return;
    }

    if (activeMode === 'assign-guest' && pendingGuest) {
      const target = findElementAtPosition(x, y);
      if (target) {
        handleAssignGuestToElement(target, pendingGuest);
        setPendingGuest(null);
        setActiveMode('select');
        showNotification(t('venue.floorPlanEditor.guestAssigned', 'Guest assigned'));
        e.preventDefault();
        return;
      }
    }

    // Selection and dragging
    const touchedElement = findElementAtPosition(x, y);
    if (touchedElement) {
      setSelectedElement(touchedElement);
      setIsDragging(true);
      setIsTouchDragging(true);
      setTouchStart({ x, y });
      setTouchElementStart({ x: touchedElement.x, y: touchedElement.y });
      e.preventDefault();
    } else {
      setSelectedElement(null);
      setIsDragging(false);
      setIsTouchDragging(false);
    }
  };

  const handleTouchMove = (e) => {
    if (!isTouchDragging || !canvasRef.current) return;
    if (e.touches.length !== 1) return;
    
    const touch = e.touches[0];
    const { x, y } = getTouchPos(touch);
    setMousePosition({ x, y });
    
    if (isDragging && selectedElement) {
      const deltaX = x - touchStart.x;
      const deltaY = y - touchStart.y;
      const newX = touchElementStart.x + deltaX;
      const newY = touchElementStart.y + deltaY;
      
      const updatedElements = elements.map(el => 
        el.id === selectedElement.id ? { ...el, x: newX, y: newY } : el
      );
      
      setElements(updatedElements);
      setSelectedElement({ ...selectedElement, x: newX, y: newY });
      e.preventDefault();
    }
  };

  const handleTouchEnd = () => {
    if (isTouchDragging && selectedElement) {
      saveToHistory();
    }
    setIsDragging(false);
    setIsResizing(false);
    setIsTouchDragging(false);
    setTouchResizeHandle(null);
  };

  // Mouse event handlers (for desktop)
  const handleMouseDown = (e) => {
    if (!canvasRef.current || isMobile) return;

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    const mouseX = (e.clientX - canvasRect.left) * scaleX / zoom;
    const mouseY = (e.clientY - canvasRect.top) * scaleY / zoom;

    setMousePosition({ x: mouseX, y: mouseY });

    if (selectedElement) {
      const handle = getResizeHandleAtPosition(mouseX, mouseY);
      if (handle) {
        setIsResizing(true);
        setResizeHandle(handle);
        setDragStart({x: mouseX, y: mouseY});
        setElementStartDimensions({
          width: selectedElement.width,
          height: selectedElement.height
        });
        setElementStart({
          x: selectedElement.x,
          y: selectedElement.y
        });
        return;
      }
    }

    const clickedElement = findElementAtPosition(mouseX, mouseY);

    if (clickedElement) {
      setDragStart({x: mouseX, y: mouseY});
      setElementStart({x: clickedElement.x, y: clickedElement.y});
      setSelectedElement(clickedElement);
      setIsDragging(true);
    } else {
      setSelectedElement(null);
      setIsDragging(false);
    }
  };

  const handleMouseMove = (e) => {
    if (!canvasRef.current || isMobile) return;

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    const mouseX = (e.clientX - canvasRect.left) * scaleX / zoom;
    const mouseY = (e.clientY - canvasRect.top) * scaleY / zoom;

    setMousePosition({ x: mouseX, y: mouseY });

    if ((!isDragging && !isResizing) || !selectedElement) return;

    if (isResizing) {
      const deltaX = mouseX - dragStart.x;
      const deltaY = mouseY - dragStart.y;

      const minWidth = 20;
      const minHeight = 20;

      let newX = elementStart.x;
      let newY = elementStart.y;
      let newWidth = elementStartDimensions.width;
      let newHeight = elementStartDimensions.height;

      switch (resizeHandle) {
        case 'nw':
          newX = elementStart.x + deltaX;
          newY = elementStart.y + deltaY;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 'ne':
          newY = elementStart.y + deltaY;
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 'sw':
          newX = elementStart.x + deltaX;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'se':
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'n':
          newY = elementStart.y + deltaY;
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 's':
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'w':
          newX = elementStart.x + deltaX;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          break;
        case 'e':
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          break;
      }

      const updatedElements = elements.map(element => {
        if (element.id === selectedElement.id) {
          return { ...element, x: newX, y: newY, width: newWidth, height: newHeight };
        }
        return element;
      });

      setElements(updatedElements);
      setSelectedElement({...selectedElement, x: newX, y: newY, width: newWidth, height: newHeight});
    } else if (isDragging) {
      const deltaX = mouseX - dragStart.x;
      const deltaY = mouseY - dragStart.y;

      const newX = elementStart.x + deltaX;
      const newY = elementStart.y + deltaY;

      const updatedElements = elements.map(element => {
        if (element.id === selectedElement.id) {
          return { ...element, x: newX, y: newY };
        }
        return element;
      });

      setElements(updatedElements);
      setSelectedElement({...selectedElement, x: newX, y: newY});
    }
  };

  const handleMouseUp = () => {
    if ((isDragging || isResizing) && selectedElement) {
      saveToHistory();
    }

    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle(null);
  };

  const handleMouseLeave = () => {
    if (isDragging || isResizing) {
      saveToHistory();
    }

    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle(null);
  };

  // Utility functions
  const handleAddElementAt = (x, y, type) => {
    const newElement = createFloorPlanElement(type, x, y);
    saveToHistory();
    setElements([...elements, newElement]);
    setSelectedElement(newElement);
  };

  const handleAssignGuestToElement = (element, guest) => {
    const updatedElements = elements.map(el => {
      if (el.id === element.id) {
        if (el.type === 'seat') return { ...el, assignedGuests: [guest._id] };
        if (el.type === 'table') {
          const current = Array.isArray(el.assignedGuests) ? [...el.assignedGuests] : [];
          if (!current.includes(guest._id)) return { ...el, assignedGuests: [...current, guest._id] };
        }
      }
      return el;
    });
    setElements(updatedElements);
    setSelectedElement({ ...element, assignedGuests: [guest._id] });
    saveToHistory();
  };

  const showNotification = (message) => {
    setSnackbarMessage(message);
    setShowSnackbar(true);
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.1, 0.5));
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setElements([...history[historyIndex - 1]]);
      setSelectedElement(null);
      showNotification(t('venue.floorPlanEditor.undone', 'Action undone'));
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setElements([...history[historyIndex + 1]]);
      setSelectedElement(null);
      showNotification(t('venue.floorPlanEditor.redone', 'Action redone'));
    }
  };

  const handleSave = () => {
    if (elements && Array.isArray(elements)) {
      onSave(elements, backgroundImageUrl, canvasWidth, canvasHeight);
    }
  };

  const handleUploadImage = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setBackgroundImageUrl(e.target.result);
        showNotification(t('venue.floorPlanEditor.imageUploaded', 'Background image uploaded'));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleOpenGuestDialog = () => {
    if (selectedElement) {
      if (selectedElement.type === 'seat') {
        setIsTableAssignment(false);
        setOpenGuestDialog(true);
      } else if (selectedElement.type === 'table') {
        setIsTableAssignment(true);
        setOpenGuestDialog(true);
      }
    }
  };

  const handleCloseGuestDialog = () => {
    setOpenGuestDialog(false);
  };

  const handleAssignGuest = (guestId) => {
    if (!selectedElement) return;

    let guestIds;

    if (selectedElement.type === 'seat') {
      guestIds = guestId ? [guestId] : [];
    } else {
      guestIds = Array.isArray(guestId) ? [...guestId] : [];
    }

    const updatedElements = elements.map(element => {
      if (element.id === selectedElement.id) {
        return {
          ...element,
          assignedGuests: [...guestIds]
        };
      }
      return element;
    });

    setElements(updatedElements);

    const updatedSelectedElement = {
      ...selectedElement,
      assignedGuests: [...guestIds]
    };

    setSelectedElement(updatedSelectedElement);
    saveToHistory();
    handleCloseGuestDialog();
    showNotification(t('venue.floorPlanEditor.guestAssigned', 'Guest(s) assigned successfully'));
  };

  const handleDeleteElement = () => {
    if (selectedElement) {
      saveToHistory();
      setElements(elements.filter(element => element.id !== selectedElement.id));
      setSelectedElement(null);
      setAnchorEl(null);
      showNotification(t('venue.floorPlanEditor.elementDeleted', 'Element deleted'));
    }
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Mobile-specific render
  if (isMobile) {
    return (
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Mobile Toolbar */}
        <AppBar position="static" color="default" elevation={1}>
          <Toolbar variant="dense">
            <IconButton
              edge="start"
              onClick={handleUndo}
              disabled={historyIndex <= 0}
              size="small"
            >
              <UndoIcon />
            </IconButton>
            <IconButton
              onClick={handleRedo}
              disabled={historyIndex >= history.length - 1}
              size="small"
            >
              <RedoIcon />
            </IconButton>
            <Box sx={{ flexGrow: 1 }} />
            <IconButton onClick={handleZoomOut} size="small">
              <ZoomOutIcon />
            </IconButton>
            <Typography variant="caption" sx={{ mx: 1 }}>
              {Math.round(zoom * 100)}%
            </Typography>
            <IconButton onClick={handleZoomIn} size="small">
              <ZoomInIcon />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* Mobile Tabs */}
        <Tabs
          value={mobileTab}
          onChange={(e, newValue) => setMobileTab(newValue)}
          variant="fullWidth"
          indicatorColor="primary"
        >
          <Tab label="Canvas" icon={<MapIcon />} iconPosition="start" />
          <Tab label="Guests" icon={<GroupIcon />} iconPosition="start" />
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>
          {mobileTab === 0 && (
            <>
              {/* Action Buttons */}
              <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={<TouchAppIcon />}
                  label="Select"
                  onClick={() => setActiveMode('select')}
                  color={activeMode === 'select' ? 'primary' : 'default'}
                  variant={activeMode === 'select' ? 'filled' : 'outlined'}
                />
                <Chip
                  icon={<TableRestaurantIcon />}
                  label="Add Table"
                  onClick={() => {
                    setActiveMode('add-table');
                    showNotification(t('venue.floorPlanEditor.tapToAddTable', 'Tap on canvas to add table'));
                  }}
                  color={activeMode === 'add-table' ? 'primary' : 'default'}
                  variant={activeMode === 'add-table' ? 'filled' : 'outlined'}
                />
                <Chip
                  icon={<EventSeatIcon />}
                  label="Add Seat"
                  onClick={() => {
                    setActiveMode('add-seat');
                    showNotification(t('venue.floorPlanEditor.tapToAddSeat', 'Tap on canvas to add seat'));
                  }}
                  color={activeMode === 'add-seat' ? 'primary' : 'default'}
                  variant={activeMode === 'add-seat' ? 'filled' : 'outlined'}
                />
                {selectedElement && (
                  <>
                    <Chip
                      icon={<DeleteIcon />}
                      label="Delete"
                      onClick={handleDeleteElement}
                      color="error"
                      variant="outlined"
                    />
                    {(selectedElement.type === 'table' || selectedElement.type === 'seat') && (
                      <Chip
                        icon={<PersonAddIcon />}
                        label="Assign"
                        onClick={handleOpenGuestDialog}
                        color="secondary"
                        variant="outlined"
                      />
                    )}
                  </>
                )}
              </Box>

              {/* Canvas */}
              <Paper
                variant="outlined"
                sx={{
                  overflow: 'auto',
                  WebkitOverflowScrolling: 'touch',
                  border: activeMode !== 'select' ? '2px dashed #1976d2' : '1px solid #e0e0e0',
                }}
              >
                <canvas
                  ref={canvasRef}
                  width={canvasWidth}
                  height={canvasHeight}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                  style={{
                    display: 'block',
                    touchAction: 'none',
                    backgroundColor: '#fafafa',
                  }}
                />
              </Paper>

              {/* Selected Element Info */}
              {selectedElement && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle2">
                      {selectedElement.type === 'table' ? 'Table' : 'Seat'}: {selectedElement.label || selectedElement.id}
                    </Typography>
                    {selectedElement.assignedGuests?.length > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        {selectedElement.assignedGuests.length} guest(s) assigned
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {mobileTab === 1 && (
            <Box>
              <TextField
                fullWidth
                size="small"
                placeholder={t('venue.floorPlanEditor.searchGuests', 'Search guests...')}
                value={guestSearchTerm}
                onChange={(e) => setGuestSearchTerm(e.target.value)}
                sx={{ mb: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />

              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                {t('venue.floorPlanEditor.tapGuestToSelect', 'Tap a guest, then switch to Canvas tab and tap a table/seat')}
              </Typography>

              <List>
                {filteredGuests.map((guest) => (
                  <Card
                    key={guest._id}
                    sx={{
                      mb: 1,
                      border: pendingGuest?._id === guest._id ? '2px solid' : '1px solid',
                      borderColor: pendingGuest?._id === guest._id ? 'primary.main' : 'divider',
                    }}
                  >
                    <ListItemButton
                      onClick={() => {
                        setPendingGuest(guest);
                        setActiveMode('assign-guest');
                        showNotification(t('venue.floorPlanEditor.guestSelected', 'Guest selected. Switch to Canvas tab to assign.'));
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: pendingGuest?._id === guest._id ? 'primary.main' : 'grey.400' }}>
                          {guest.name.charAt(0).toUpperCase()}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={guest.name}
                        secondary={
                          <>
                            {guest.email}
                            <Chip
                              label={guest.rsvpStatus}
                              color={
                                guest.rsvpStatus === 'Confirmed' ? 'success' :
                                guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                              }
                              size="small"
                              sx={{ ml: 1, height: 18 }}
                            />
                          </>
                        }
                      />
                    </ListItemButton>
                  </Card>
                ))}
              </List>
            </Box>
          )}
        </Box>

        {/* Save Button */}
        <Box sx={{ p: 2 }}>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={handleSave}
          >
            {t('venue.floorPlanEditor.save', 'Save Floor Plan')}
          </Button>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={showSnackbar}
          autoHideDuration={3000}
          onClose={() => setShowSnackbar(false)}
          message={snackbarMessage}
        />

        {/* Guest Assignment Dialog */}
        <Dialog
          open={openGuestDialog}
          onClose={handleCloseGuestDialog}
          fullScreen
        >
          <AppBar sx={{ position: 'relative' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={handleCloseGuestDialog}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
              <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                {isTableAssignment ?
                  t('venue.floorPlanEditor.assignGuestsToTable', 'Assign Guests to Table') :
                  t('venue.floorPlanEditor.assignGuestToSeat', 'Assign Guest to Seat')}
              </Typography>
              <Button
                autoFocus
                color="inherit"
                onClick={() => {
                  if (guestSelectorRef.current) {
                    guestSelectorRef.current.handleAssign();
                  }
                }}
              >
                {t('common.save', 'Save')}
              </Button>
            </Toolbar>
          </AppBar>
          <DialogContent>
            <GuestAssignmentSelector
              ref={guestSelectorRef}
              guests={guestList}
              onAssign={handleAssignGuest}
              isMultiSelect={isTableAssignment}
              currentAssignments={selectedElement?.assignedGuests || []}
              isMobile={true}
            />
          </DialogContent>
        </Dialog>
      </Box>
    );
  }

  // Desktop/Tablet render
  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      {/* Desktop Toolbar */}
      <Box sx={{ mb: 2, display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 1, md: 0 } }}>
          <Button
            variant="outlined"
            component="label"
            size="small"
            startIcon={<AddIcon />}
          >
            {t('venue.floorPlanEditor.uploadImage', 'Upload Image')}
            <input
              ref={fileInputRef}
              type="file"
              hidden
              accept="image/*"
              onChange={handleUploadImage}
            />
          </Button>
        </Box>
        <Box>
          <Tooltip title="Zoom In">
            <IconButton onClick={handleZoomIn} size="small" sx={{ mr: 1 }}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={handleZoomOut} size="small" sx={{ mr: 1 }}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Reset Zoom">
            <IconButton onClick={() => setZoom(1)} size="small" sx={{ mr: 1 }}>
              <Typography variant="caption" sx={{ fontSize: '0.7rem', fontWeight: 'bold' }}>
                1:1
              </Typography>
            </IconButton>
          </Tooltip>
          <Typography variant="caption" sx={{ mr: 1, minWidth: '40px', textAlign: 'center' }}>
            {Math.round(zoom * 100)}%
          </Typography>
          <Tooltip title="Undo">
            <span>
              <IconButton
                onClick={handleUndo}
                size="small"
                sx={{ mr: 1 }}
                disabled={historyIndex <= 0}
              >
                <UndoIcon />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="Redo">
            <span>
              <IconButton
                onClick={handleRedo}
                size="small"
                sx={{ mr: 1 }}
                disabled={historyIndex >= history.length - 1}
              >
                <RedoIcon />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="More Actions">
            <span>
              <IconButton
                onClick={handleMenuOpen}
                size="small"
                disabled={!selectedElement}
              >
                <MoreVertIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>
      </Box>

      {/* Main Content */}
      <Box sx={{
        display: 'flex',
        flexDirection: 'row',
        height: 'calc(100% - 100px)',
        gap: 2,
        overflow: 'hidden',
      }}>
        {/* Element Panel */}
        <Paper
          variant="outlined"
          sx={{
            width: isTablet ? 120 : 160,
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            overflow: 'auto',
            flexShrink: 0,
          }}
        >
          <Typography variant="subtitle2" align="center">
            {t('venue.floorPlanEditor.dragElements', 'Drag Elements')}
          </Typography>
          <Paper
            sx={{
              p: 2,
              width: '100%',
              cursor: 'grab',
              bgcolor: '#ffcc80',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              border: '1px solid #ccc',
            }}
            draggable
            onDragStart={(e) => handleElementDragStart(e, 'table')}
          >
            <TableRestaurantIcon fontSize="large" />
            <Typography variant="caption">{t('venue.floorPlanEditor.table', 'Table')}</Typography>
          </Paper>
          <Paper
            sx={{
              p: 2,
              width: '100%',
              cursor: 'grab',
              bgcolor: '#81c784',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              border: '1px solid #ccc',
            }}
            draggable
            onDragStart={(e) => handleElementDragStart(e, 'seat')}
          >
            <EventSeatIcon fontSize="large" />
            <Typography variant="caption">{t('venue.floorPlanEditor.seat', 'Seat')}</Typography>
          </Paper>
        </Paper>

        {/* Canvas */}
        <Paper
          variant="outlined"
          sx={{
            flexGrow: 1,
            overflow: 'auto',
            position: 'relative',
            p: 1,
          }}
          ref={canvasContainerRef}
          onDrop={handleCanvasDrop}
          onDragOver={handleCanvasDragOver}
        >
          <canvas
            ref={canvasRef}
            width={canvasWidth}
            height={canvasHeight}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{
              cursor: isResizing ? getResizeCursor(resizeHandle) : (isDragging ? 'grabbing' : 'default'),
              display: 'block',
              border: '1px solid #ccc',
              backgroundColor: '#fafafa',
            }}
          />
        </Paper>

        {/* Guest List Panel */}
        <Paper
          variant="outlined"
          sx={{
            width: isTablet ? 250 : 300,
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            flexShrink: 0,
          }}
        >
          <Typography variant="subtitle2" gutterBottom align="center">
            {t('venue.floorPlanEditor.guestList', 'Guest List')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            placeholder={t('venue.floorPlanEditor.searchGuests', 'Search guests...')}
            value={guestSearchTerm}
            onChange={(e) => setGuestSearchTerm(e.target.value)}
            sx={{ mb: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="caption" align="center" sx={{ mb: 1 }}>
            {t('venue.floorPlanEditor.dragGuestsHere', 'Drag guests onto tables or seats')}
          </Typography>
          <Box sx={{ overflow: 'auto', flexGrow: 1 }}>
            <List dense>
              {filteredGuests.map((guest) => (
                <ListItem
                  key={guest._id}
                  disablePadding
                  sx={{
                    mb: 1,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                    cursor: 'grab',
                  }}
                  draggable
                  onDragStart={(e) => handleGuestDragStart(e, guest)}
                >
                  <ListItemButton dense>
                    <Box sx={{ width: '100%' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {guest.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" noWrap>
                        {guest.email}
                      </Typography>
                      <Box sx={{ mt: 0.5 }}>
                        <Chip
                          label={guest.rsvpStatus}
                          color={
                            guest.rsvpStatus === 'Confirmed' ? 'success' :
                            guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                          }
                          size="small"
                          sx={{ height: 20, fontSize: '0.7rem' }}
                        />
                      </Box>
                    </Box>
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>
        </Paper>
      </Box>

      {/* Bottom Actions */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {selectedElement && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ mr: 2 }}>
              {t('venue.floorPlanEditor.selected', 'Selected')}: <strong>{selectedElement.type === 'table' ? t('venue.floorPlanEditor.table', 'Table') : t('venue.floorPlanEditor.seat', 'Seat')} {selectedElement.label || selectedElement.id}</strong>
            </Typography>
            {(selectedElement.type === 'seat' || selectedElement.type === 'table') && (
              <Button
                variant="outlined"
                size="small"
                onClick={handleOpenGuestDialog}
                startIcon={selectedElement.type === 'seat' ? <EventSeatIcon /> : <TableRestaurantIcon />}
              >
                {selectedElement.assignedGuests?.length > 0 ? 
                  t('venue.floorPlanEditor.changeAssignment', 'Change Assignment') : 
                  t('venue.floorPlanEditor.assignGuest', 'Assign Guest')}
              </Button>
            )}
          </Box>
        )}
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
        >
          {t('venue.floorPlanEditor.save', 'Save Floor Plan')}
        </Button>
      </Box>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleDeleteElement}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('venue.floorPlanEditor.deleteElement', 'Delete Element')}</ListItemText>
        </MenuItem>
      </Menu>

      {/* Guest Assignment Dialog */}
      <Dialog
        open={openGuestDialog}
        onClose={handleCloseGuestDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {isTableAssignment ?
            `${t('venue.floorPlanEditor.assignGuestsToTable', 'Assign Guests to Table')} ${selectedElement?.label || ''}` :
            `${t('venue.floorPlanEditor.assignGuestToSeat', 'Assign Guest to Seat')} ${selectedElement?.label || ''}`}
        </DialogTitle>
        <DialogContent dividers>
          <GuestAssignmentSelector
            ref={guestSelectorRef}
            guests={guestList}
            onAssign={handleAssignGuest}
            isMultiSelect={isTableAssignment}
            currentAssignments={selectedElement?.assignedGuests || []}
            isMobile={false}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseGuestDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={() => {
              if (guestSelectorRef.current) {
                guestSelectorRef.current.handleAssign();
              }
            }}
            color="primary"
            variant="contained"
          >
            {t('common.apply', 'Apply')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Main VenueManagement Component
const VenueManagement = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentEvent } = useContext(EventContext) || { currentEvent: null };
  const { user } = useContext(AuthContext) || { user: null };
  
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.between('lg', 'xl'));
  const isUltraWide = useMediaQuery(theme.breakpoints.up('xl'));

  // State management
  const [venues, setVenues] = useState([]);
  const [guests, setGuests] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingVenue, setEditingVenue] = useState(null);
  const [newVenue, setNewVenue] = useState({
    name: '',
    address: '',
    capacity: 1,
    description: '',
    image: 'https://via.placeholder.com/300x200?text=New+Venue',
    floorPlan: null
  });
  const [selectedVenue, setSelectedVenue] = useState(null);
  const [openFloorPlanDialog, setOpenFloorPlanDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Helper functions
  const handleError = (error, defaultMessage) => {
    const message = error.message || defaultMessage;
    setSnackbar({ open: true, message, severity: 'error' });
  };

  const validateVenue = (venue) => {
    if (!venue.name.trim()) {
      handleError(null, t('errors.required', 'Name is required'));
      return false;
    }
    if (!venue.address.trim()) {
      handleError(null, t('errors.required', 'Address is required'));
      return false;
    }
    const capacity = parseInt(venue.capacity, 10);
    if (isNaN(capacity) || capacity < 0) {
      handleError(null, t('venue.form.capacityHelperText', 'Capacity must be a positive number'));
      return false;
    }
    return true;
  };

  const getSampleVenue = () => ({
    _id: '1',
    name: 'Sample Venue',
    address: '123 Sample St',
    capacity: 100,
    description: 'A sample venue for testing',
    floorPlan: { width: 1000, height: 800, elements: [], background: null }
  });

  const getSampleGuests = () => [
    { _id: '1', name: 'John Doe', email: '<EMAIL>', rsvpStatus: 'Confirmed' },
    { _id: '2', name: 'Jane Smith', email: '<EMAIL>', rsvpStatus: 'Pending' },
    { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', rsvpStatus: 'Confirmed' }
  ];

  // Data fetching
  useEffect(() => {
    if (!currentEvent) return;

    const fetchData = async () => {
      setIsLoading(true);
      setError('');
      try {
        const token = localStorage.getItem('token');

        if (!currentEvent?._id) {
          setError('No event selected. Please select an event first.');
          setIsLoading(false);
          return;
        }

        // Fetch venues
        try {
          if (token) {
            const venuesResponse = await fetch(`/api/resources/venues?eventId=${currentEvent._id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (venuesResponse.ok) {
              const venuesData = await venuesResponse.json();
              setVenues(venuesData);
              if (venuesData.length > 0 && !selectedVenue) {
                setSelectedVenue(venuesData[0]);
              }
            } else {
              setVenues([getSampleVenue()]);
            }
          } else {
            setVenues([getSampleVenue()]);
          }
        } catch (venueError) {
          setVenues([getSampleVenue()]);
        }

        // Fetch guests
        try {
          if (token) {
            const guestsResponse = await fetch(`/api/resources/guests?eventId=${currentEvent._id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (guestsResponse.ok) {
              const guestsData = await guestsResponse.json();
              setGuests(guestsData);
            } else {
              setGuests(getSampleGuests());
            }
          } else {
            setGuests(getSampleGuests());
          }
        } catch (guestError) {
          setGuests(getSampleGuests());
        }
      } catch (error) {
        setError(`Could not load venues or guests: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentEvent]);

  // Event handlers
  const handleOpenDialog = (venue = null) => {
    if (venue) {
      setEditingVenue(venue);
      const venueData = { ...venue };
      venueData.capacity = parseInt(venue.capacity, 10) || 1;
      setNewVenue(venueData);
    } else {
      setEditingVenue(null);
      setNewVenue({
        name: '',
        address: '',
        capacity: 1,
        description: '',
        image: 'https://via.placeholder.com/300x200?text=New+Venue',
        floorPlan: null
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingVenue(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewVenue({ ...newVenue, [name]: value });
  };

  const handleSaveVenue = async () => {
    if (!currentEvent) {
      handleError(null, t('guests.errors.selectEventFirst', 'Please select an event first'));
      return;
    }

    if (!validateVenue(newVenue)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error(t('errors.authTokenNotFound', 'Authentication token not found. Please log in again.'));
      }

      const capacity = parseInt(newVenue.capacity, 10);
      const venueData = {
        ...newVenue,
        capacity: capacity,
        name: newVenue.name.trim(),
        address: newVenue.address.trim(),
        description: newVenue.description.trim()
      };

      const useDevRoute = !token || !user;

      if (editingVenue) {
        const endpoint = useDevRoute
          ? `/api/resources/venues/dev-update/${editingVenue._id}`
          : `/api/resources/venues/${editingVenue._id}`;

        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify(venueData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || t('venue.errors.updateFailed', 'Failed to update venue'));
        }

        const updatedVenue = await response.json();
        setVenues(venues.map(v => v._id === editingVenue._id ? updatedVenue : v));
        setSnackbar({ open: true, message: t('venue.venueUpdated', 'Venue updated successfully'), severity: 'success' });
      } else {
        const endpoint = useDevRoute ? '/api/resources/venues/dev-create' : '/api/resources/venues';

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify({
            ...venueData,
            eventId: currentEvent._id,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || t('venue.errors.createFailed', 'Failed to create venue'));
        }

        const createdVenue = await response.json();
        setVenues([...venues, createdVenue]);
        setSnackbar({ open: true, message: t('venue.venueCreated', 'Venue created successfully'), severity: 'success' });
      }
      handleCloseDialog();
    } catch (error) {
      handleError(error, t('venue.errors.saveError', 'Error saving venue'));
    }
  };

  const handleDeleteVenue = async (id) => {
    if (!window.confirm(t('venue.confirmDelete', 'Are you sure you want to delete this venue?'))) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const useDevRoute = !token || !user;

      const endpoint = useDevRoute
        ? `/api/resources/venues/dev-delete/${id}`
        : `/api/resources/venues/${id}`;

      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Authorization': useDevRoute ? '' : `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || t('venue.errors.deleteFailed', 'Failed to delete venue'));
      }

      setVenues(venues.filter(venue => venue._id !== id));
      if (selectedVenue && selectedVenue._id === id) {
        setSelectedVenue(null);
      }
      setSnackbar({ open: true, message: t('venue.venueDeleted', 'Venue deleted successfully'), severity: 'success' });
    } catch (error) {
      handleError(error, t('venue.errors.deleteError', 'Error deleting venue'));
    }
  };

  const handleSelectVenue = (venue) => {
    setSelectedVenue(venue);
    if (isMobile) {
      setMobileDrawerOpen(false);
    }
  };

  const handleOpenFloorPlanDesigner = () => {
    if (!selectedVenue) {
      handleError(null, t('venue.selectVenueFirst', 'Please select a venue first'));
      return;
    }

    if (!selectedVenue._id) {
      handleError(null, t('venue.invalidVenueSelected', 'Invalid venue selected'));
      return;
    }

    setTimeout(() => {
      setOpenFloorPlanDialog(true);
    }, 50);
  };

  const handleCloseFloorPlanDialog = () => {
    setOpenFloorPlanDialog(false);
  };

  const handleSaveFloorPlan = async (floorPlanElements, backgroundImage, width, height) => {
    if (!selectedVenue) {
      handleError(null, t('venue.selectVenueFirst', 'Please select a venue first'));
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error(t('errors.authTokenNotFound', 'Authentication token not found'));
      }

      const finalElements = validateGuestAssignments(floorPlanElements, guests);

      const cleanElements = finalElements.map(element => {
        const cleanElement = { ...element };
        cleanElement.assignedGuests = Array.isArray(cleanElement.assignedGuests) ?
          [...cleanElement.assignedGuests] : [];
        if ('assignedGuest' in cleanElement) {
          delete cleanElement.assignedGuest;
        }
        return cleanElement;
      });

      const floorPlanData = {
        width: width || 800,
        height: height || 600,
        elements: cleanElements,
        background: backgroundImage
      };

      const response = await fetch(`/api/resources/venues/${selectedVenue._id}/floorplan`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ floorPlan: floorPlanData }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || t('venue.errors.saveFloorPlanFailed', 'Failed to save floor plan'));
      }

      const updatedVenue = await response.json();
      setVenues(venues.map(v => v._id === selectedVenue._id ? updatedVenue : v));
      setSelectedVenue(updatedVenue);
      handleCloseFloorPlanDialog();
      setSnackbar({ open: true, message: t('venue.floorPlanSaved', 'Floor plan saved successfully'), severity: 'success' });
    } catch (error) {
      handleError(error, t('venue.errors.saveFloorPlanFailed', 'Failed to save floor plan'));
    }
  };

  // No event selected
  if (!currentEvent) {
    return (
      <Box sx={{ 
        width: '100%', 
        height: isMobile ? 'calc(100vh - 56px)' : 'calc(100vh - 64px)', 
        display: 'flex',
        flexDirection: 'column',
        p: isMobile ? 2 : 3
      }}>
        <Paper sx={{ 
          flexGrow: 1,
          p: isMobile ? 2 : 3,
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <PlaceIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
            {t('venue.title', 'Venue Management')}
          </Typography>
          <Alert severity="info" sx={{ maxWidth: 600 }}>
            {t('venue.noEventSelected', 'Please select an event to manage venues.')}
          </Alert>
        </Paper>
      </Box>
    );
  }

  // Mobile render
  if (isMobile) {
    return (
      <Box sx={{ width: '100%', height: 'calc(100vh - 56px)', display: 'flex', flexDirection: 'column' }}>
        {/* Mobile Header */}
        <AppBar position="static" color="default">
          <Toolbar>
            <IconButton
              edge="start"
              onClick={() => setMobileDrawerOpen(true)}
              aria-label="menu"
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              {t('venue.title', 'Venues')}
            </Typography>
            <IconButton onClick={() => handleOpenDialog()} color="primary">
              <AddIcon />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* Mobile Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Typography>{t('common.loading', 'Loading...')}</Typography>
            </Box>
          ) : selectedVenue ? (
            <>
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {selectedVenue.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <LocationOnIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                    {selectedVenue.address}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <GroupIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                    {selectedVenue.capacity} {t('common.people', 'people')}
                  </Typography>
                  {selectedVenue.description && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {selectedVenue.description}
                    </Typography>
                  )}
                </CardContent>
                <CardActions>
                  <Button size="small" startIcon={<EditIcon />} onClick={() => handleOpenDialog(selectedVenue)}>
                    {t('common.edit', 'Edit')}
                  </Button>
                  <Button size="small" startIcon={<MapIcon />} onClick={handleOpenFloorPlanDesigner}>
                    {selectedVenue.floorPlan ? t('venue.editFloorPlan', 'Edit Floor Plan') : t('venue.createFloorPlan', 'Create Floor Plan')}
                  </Button>
                </CardActions>
              </Card>

              {selectedVenue.floorPlan ? (
                <Paper variant="outlined" sx={{ overflow: 'hidden' }}>
                  <FloorPlanPreview venue={selectedVenue} guestList={guests} />
                </Paper>
              ) : (
                <Card sx={{ textAlign: 'center', py: 4 }}>
                  <PlaceIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    {t('venue.noFloorPlanYet', 'No Floor Plan Yet')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2, px: 2 }}>
                    {t('venue.createFloorPlanDescription', 'Create a floor plan to arrange tables and seats')}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleOpenFloorPlanDesigner}
                  >
                    {t('venue.createFloorPlan', 'Create Floor Plan')}
                  </Button>
                </Card>
              )}
            </>
          ) : (
            <Card sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                {t('venue.selectVenuePrompt', 'Select a venue from the menu')}
              </Typography>
            </Card>
          )}
        </Box>

        {/* Mobile Drawer */}
        <SwipeableDrawer
          anchor="left"
          open={mobileDrawerOpen}
          onClose={() => setMobileDrawerOpen(false)}
          onOpen={() => setMobileDrawerOpen(true)}
        >
          <Box sx={{ width: 280, p: 2 }}>
            <VenueList
              venues={venues}
              selectedVenue={selectedVenue}
              onVenueSelect={handleSelectVenue}
              onAddVenue={() => {
                setMobileDrawerOpen(false);
                handleOpenDialog();
              }}
              onEditVenue={(venue) => {
                setMobileDrawerOpen(false);
                handleOpenDialog(venue);
              }}
              onDeleteVenue={handleDeleteVenue}
              t={t}
              isMobile={true}
            />
          </Box>
        </SwipeableDrawer>

        {/* Venue Dialog */}
        <Dialog 
          open={openDialog} 
          onClose={handleCloseDialog} 
          fullScreen={isMobile}
        >
          {isMobile && (
            <AppBar sx={{ position: 'relative' }}>
              <Toolbar>
                <IconButton
                  edge="start"
                  color="inherit"
                  onClick={handleCloseDialog}
                  aria-label="close"
                >
                  <CloseIcon />
                </IconButton>
                <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                  {editingVenue ? t('venue.editVenue', 'Edit Venue') : t('venue.addNewVenue', 'Add New Venue')}
                </Typography>
                <Button
                  autoFocus
                  color="inherit"
                  onClick={handleSaveVenue}
                  disabled={!newVenue.name.trim() || !newVenue.address.trim()}
                >
                  {t('common.save', 'Save')}
                </Button>
              </Toolbar>
            </AppBar>
          )}
          {!isMobile && (
            <DialogTitle>
              {editingVenue ? t('venue.editVenue', 'Edit Venue') : t('venue.addNewVenue', 'Add New Venue')}
            </DialogTitle>
          )}
          <DialogContent dividers={!isMobile}>
            <Box sx={{ pt: isMobile ? 2 : 0 }}>
              <TextField
                fullWidth
                label={`${t('venue.form.name', 'Name')} *`}
                name="name"
                value={newVenue.name}
                onChange={handleInputChange}
                required
                size={isMobile ? "medium" : "small"}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label={`${t('venue.form.address', 'Address')} *`}
                name="address"
                value={newVenue.address}
                onChange={handleInputChange}
                required
                size={isMobile ? "medium" : "small"}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label={`${t('venue.form.capacity', 'Capacity')} *`}
                name="capacity"
                type="number"
                value={newVenue.capacity}
                onChange={handleInputChange}
                required
                inputProps={{ min: 0 }}
                size={isMobile ? "medium" : "small"}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label={t('venue.form.description', 'Description')}
                name="description"
                value={newVenue.description}
                onChange={handleInputChange}
                multiline
                rows={4}
                size={isMobile ? "medium" : "small"}
              />
            </Box>
          </DialogContent>
          {!isMobile && (
            <DialogActions>
              <Button onClick={handleCloseDialog}>{t('common.cancel', 'Cancel')}</Button>
              <Button
                onClick={handleSaveVenue}
                variant="contained"
                color="primary"
                disabled={!newVenue.name.trim() || !newVenue.address.trim()}
              >
                {t('common.save', 'Save')}
              </Button>
            </DialogActions>
          )}
        </Dialog>

        {/* Floor Plan Dialog */}
        <Dialog
          open={openFloorPlanDialog}
          onClose={handleCloseFloorPlanDialog}
          fullScreen
        >
          <AppBar sx={{ position: 'relative' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={handleCloseFloorPlanDialog}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
              <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                {t('venue.designFloorPlan', 'Design Floor Plan')}
              </Typography>
            </Toolbar>
          </AppBar>
          <DialogContent sx={{ p: 0 }}>
            <FloorPlanDesigner
              venue={selectedVenue}
              onSave={handleSaveFloorPlan}
              guestList={guests}
              isMobile={true}
              isTablet={false}
              isLargeScreen={false}
              isUltraWide={false}
            />
          </DialogContent>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={4000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          message={snackbar.message}
        />
      </Box>
    );
  }

  // Desktop/Tablet render
  return (
    <Box sx={{
      width: '100%',
      height: 'calc(100vh - 64px)',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <Paper sx={{
        flexGrow: 1,
        m: 2,
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            {t('venue.title', 'Venue Management')}
          </Typography>
          {currentEvent && (
            <Typography variant="subtitle1" color="text.secondary">
              {t('events.generic', 'Event')}: {currentEvent.title}
            </Typography>
          )}
        </Box>

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <Typography>{t('common.loading', 'Loading...')}</Typography>
          </Box>
        ) : (
          <Box sx={{
            display: 'flex',
            gap: 3,
            flexGrow: 1,
            overflow: 'hidden',
            minHeight: 0,
          }}>
            <VenueList
              venues={venues}
              selectedVenue={selectedVenue}
              onVenueSelect={handleSelectVenue}
              onAddVenue={() => handleOpenDialog()}
              onEditVenue={handleOpenDialog}
              onDeleteVenue={handleDeleteVenue}
              t={t}
              isMobile={false}
            />

            <Paper
              variant="outlined"
              sx={{
                flexGrow: 1,
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              }}
            >
              {selectedVenue ? (
                <>
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h5">{selectedVenue.name}</Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleOpenDialog(selectedVenue)}
                      >
                        {t('common.edit', 'Edit')}
                      </Button>
                    </Box>

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('venue.form.address', 'Address')}:</strong> {selectedVenue.address}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('venue.form.capacity', 'Capacity')}:</strong> {selectedVenue.capacity} {t('common.people', 'people')}
                        </Typography>
                      </Grid>
                      {selectedVenue.description && (
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">
                            <strong>{t('venue.form.description', 'Description')}:</strong> {selectedVenue.description}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Box>

                  <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">{t('venue.floorPlan', 'Floor Plan')}</Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={selectedVenue.floorPlan ? <EditIcon /> : <AddIcon />}
                        onClick={handleOpenFloorPlanDesigner}
                      >
                        {selectedVenue.floorPlan ? t('venue.editFloorPlan', 'Edit Floor Plan') : t('venue.createFloorPlan', 'Create Floor Plan')}
                      </Button>
                    </Box>

                    {selectedVenue.floorPlan ? (
                      <Paper
                        variant="outlined"
                        sx={{
                          flexGrow: 1,
                          overflow: 'auto',
                          minHeight: 400,
                          display: 'flex',
                          flexDirection: 'column',
                          p: 1
                        }}
                      >
                        <FloorPlanPreview
                          venue={selectedVenue}
                          guestList={guests}
                        />
                      </Paper>
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          flexGrow: 1,
                          minHeight: 400,
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}
                      >
                        <PlaceIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          {t('venue.noFloorPlanYet', 'No Floor Plan Yet')}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {t('venue.createFloorPlanDescription', 'Create a floor plan to arrange tables and seats for this venue.')}
                        </Typography>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={handleOpenFloorPlanDesigner}
                        >
                          {t('venue.createFloorPlan', 'Create Floor Plan')}
                        </Button>
                      </Paper>
                    )}
                  </Box>
                </>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('venue.selectVenuePrompt', 'Please select a venue from the list to view details and floor plan.')}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Box>
        )}
      </Paper>

      {/* Venue Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingVenue ? t('venue.editVenue', 'Edit Venue') : t('venue.addNewVenue', 'Add New Venue')}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={`${t('venue.form.name', 'Name')} *`}
                name="name"
                value={newVenue.name}
                onChange={handleInputChange}
                required
                helperText={t('common.requiredField', 'Required field')}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={`${t('venue.form.address', 'Address')} *`}
                name="address"
                value={newVenue.address}
                onChange={handleInputChange}
                required
                helperText={t('common.requiredField', 'Required field')}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={`${t('venue.form.capacity', 'Capacity')} *`}
                name="capacity"
                type="number"
                value={newVenue.capacity}
                onChange={handleInputChange}
                required
                inputProps={{ min: 0 }}
                helperText={t('venue.form.capacityHelperText', 'Required field - must be a positive number')}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('venue.form.description', 'Description')}
                name="description"
                value={newVenue.description}
                onChange={handleInputChange}
                multiline
                rows={4}
                size="small"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={handleSaveVenue}
            variant="contained"
            color="primary"
            disabled={!newVenue.name.trim() || !newVenue.address.trim() || isNaN(parseInt(newVenue.capacity, 10)) || parseInt(newVenue.capacity, 10) < 0}
          >
            {t('common.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floor Plan Dialog */}
      <Dialog
        open={openFloorPlanDialog}
        onClose={handleCloseFloorPlanDialog}
        maxWidth={false}
        fullWidth
        fullScreen={isTablet}
        PaperProps={{
          sx: {
            m: isTablet ? 0 : 2,
            width: isTablet ? '100%' : '90vw',
            maxWidth: 'none',
            height: isTablet ? '100%' : '90vh',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle>
          {t('venue.designFloorPlan', 'Design Floor Plan')} - {selectedVenue?.name}
        </DialogTitle>
        <DialogContent dividers sx={{ p: isTablet ? 2 : 3, flexGrow: 1, overflow: 'hidden' }}>
          <FloorPlanDesigner
            venue={selectedVenue}
            onSave={handleSaveFloorPlan}
            guestList={guests}
            isTablet={isTablet}
            isMobile={false}
            isLargeScreen={isLargeScreen}
            isUltraWide={isUltraWide}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFloorPlanDialog}>{t('common.cancel', 'Cancel')}</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VenueManagement;