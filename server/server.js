const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const dotenv = require('dotenv');
const passport = require('passport');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const i18n = require('./config/i18n');
const { detectLanguage } = require('./middleware/languageMiddleware');
const userRoutes = require('./routes/userRoutes');
const eventRoutes = require('./routes/eventRoutes');
const taskRoutes = require('./routes/taskRoutes');
const templateRoutes = require('./routes/templateRoutes');
const resourceRoutes = require('./routes/resourceRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const shareRoutes = require('./routes/shareRoutes');
const { errorHandler, notFound } = require('./middleware/errorMiddleware');
const { protect } = require('./middleware/authMiddleware');
const morgan = require('morgan');
const config = require('./config');
const { clearDatabase, seedDatabase } = require('./scripts/seedDatabase');
const healthRoutes = require('./routes/healthRoutes');
const languageRoutes = require('./routes/languageRoutes');
const http = require('http');
const { checkLiquibaseMigrations, runLiquibaseUpdate } = require('./utils/liquibaseCheck');

// Load environment variables
dotenv.config();

// Initialize Express
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

// Initialize i18n middleware
app.use(i18n.init);

// Language detection middleware
app.use(detectLanguage);

// Session middleware
app.use(session({
  secret: config.jwtSecret,
  resave: false,
  saveUninitialized: false,
  cookie: { secure: config.nodeEnv === 'production' }
}));

// Passport middleware
app.use(passport.initialize());

// Logger
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// File upload configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Connect to MongoDB with a timeout
const PORT = process.env.PORT || 5000;

// Setup timeout for MongoDB connection
const connectWithTimeout = () => {
  return new Promise((resolve, reject) => {
    // Set a timeout of 5 seconds
    const timeoutId = setTimeout(() => {
      mongoose.connection.close();
      reject(new Error('MongoDB connection timeout'));
    }, 5000);

    mongoose.connect(config.mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })
    .then(() => {
      clearTimeout(timeoutId);
      resolve();
    })
    .catch(err => {
      clearTimeout(timeoutId);
      reject(err);
    });
  });
};

// Configure routes based on DB availability
const setupRoutes = () => {
  // API Routes
  app.use('/api/users', userRoutes);
  app.use('/api/events', eventRoutes);
  app.use('/api/tasks', taskRoutes);
  app.use('/api/templates', templateRoutes);
  app.use('/api/resources', resourceRoutes);
  app.use('/api/payments', paymentRoutes);
  app.use('/api/share', shareRoutes);
  app.use('/api/health', healthRoutes);
  app.use('/api/language', languageRoutes);

  // Serve static assets in production
  if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../client/build')));

    app.get('*', (req, res) => {
      res.sendFile(path.resolve(__dirname, '../client/build', 'index.html'));
    });
  } else {
    app.get('/', (req, res) => {
      res.send('API is running');
    });
  }

  // Error handling middleware
  app.use(notFound);
  app.use(errorHandler);
};

// Add to your existing code where it processes command line arguments
const processArgs = () => {
  if (process.argv.includes('--seed') || process.argv.includes('-s')) {
    console.log('Seed flag detected. Will reset and seed the database on startup.');
    return true;
  }
  return false;
};

// Start the server
const startServer = async () => {
  // Create HTTP server with improved socket handling
  const server = http.createServer(app);
  
  // Configure server timeouts and keep-alive
  server.keepAliveTimeout = 65000; // 65 seconds (longer than browser timeout which is typically 60s)
  server.headersTimeout = 66000; // 66 seconds (slightly longer than keepAliveTimeout)
  
  // Handle socket errors to prevent crashes
  server.on('connection', (socket) => {
    // Handle socket errors at the server level
    socket.on('error', (err) => {
      // Log different types of socket errors with appropriate levels
      if (err.code === 'ECONNRESET') {
        // ECONNRESET is common and usually not critical - client disconnected
        console.log(`Socket connection reset: ${err.message}`);
      } else if (err.code === 'EPIPE') {
        // EPIPE means broken pipe - client disconnected while we were writing
        console.log(`Socket broken pipe: ${err.message}`);
      } else {
        // Other socket errors might be more serious
        console.warn(`Socket error (${err.code}): ${err.message}`);
      }
      // Don't terminate the server on socket errors
    });

    // Handle socket close events
    socket.on('close', (hadError) => {
      if (hadError) {
        console.log('Socket closed due to transmission error');
      }
    });

    // Set socket timeout to prevent hanging connections
    socket.setTimeout(120000, () => { // 2 minutes
      console.log('Socket timeout - closing connection');
      socket.destroy();
    });
  });

  // Handle WebSocket connections for webpack dev server
  server.on('upgrade', (request, socket, head) => {
    if (request.url === '/ws') {
      // Add error handler to the socket
      socket.on('error', (err) => {
        console.warn(`WebSocket error: ${err.message}`);
        // Don't crash on socket errors
      });
      
      // Just respond with a 200 OK to prevent errors
      socket.write('HTTP/1.1 200 OK\r\n' +
                  'Upgrade: websocket\r\n' +
                  'Connection: Upgrade\r\n' +
                  '\r\n');
      socket.end();
    }
  });

  // Start the server
  server.listen(PORT, () => {
    console.log(`Server running in ${config.nodeEnv} mode on port ${PORT}`);
    console.log(`API available at http://localhost:${PORT}/api`);
  });

  // Setup the health route first, so it's available even if DB connection fails
  app.use('/api/health', healthRoutes);

  // Check Liquibase migrations before connecting to MongoDB
  try {
    const migrationsUpToDate = await checkLiquibaseMigrations();
    if (!migrationsUpToDate) {
      if (process.env.AUTO_UPDATE_LIQUIBASE === 'true') {
        console.log('Automatically applying pending migrations...');
        await runLiquibaseUpdate();
      } else if (process.env.REQUIRE_MIGRATIONS === 'true') {
        console.error('Database migrations are not up-to-date and REQUIRE_MIGRATIONS is set to true.');
        console.error('Please run "npm run liquibase:update" to apply pending migrations.');
        process.exit(1);
      } else {
        console.warn('Database migrations are not up-to-date. Continuing without applying migrations.');
        console.warn('Set AUTO_UPDATE_LIQUIBASE=true to apply migrations automatically.');
      }
    }
  } catch (error) {
    console.warn('Failed to check Liquibase migrations:', error.message);
    console.warn('Continuing without migration check...');
  }

  // Always try to connect to MongoDB first, regardless of environment
  let isConnected = false;

  // Check if we should skip MongoDB connection (for development without MongoDB)
  const skipMongo = process.env.SKIP_MONGO === 'true';

  if (skipMongo) {
    console.log('Skipping MongoDB connection as requested by environment variable');
    isConnected = false;
  } else {
    try {
      console.log('Connecting to MongoDB...');
      // Reduce timeout for faster fallback to in-memory database
      await mongoose.connect(config.mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 5000, // 5 second timeout for faster fallback
        socketTimeoutMS: 45000, // 45 seconds for socket operations (increased from 10s)
        connectTimeoutMS: 10000, // 10 seconds to establish connection (increased from 5s)
        // Add auto reconnect options
        reconnectTries: Number.MAX_VALUE, // Always try to reconnect
        reconnectInterval: 1000, // Start with 1 second delay between retries
        family: 4 // Use IPv4, since some systems have issues with IPv6
      });
      console.log('MongoDB connected successfully');
      isConnected = true;
    } catch (error) {
      console.error('MongoDB connection failed:', error.message);
      isConnected = false;
    }
  }
  // If MongoDB connection fails or is skipped, exit the application
  if (!isConnected) {
    console.error('MongoDB connection is required for this application to function.');
    console.error('Please ensure MongoDB is running and accessible.');

    // In test environment, we'll continue because tests handle their own database connection
    if (config.nodeEnv !== 'test') {
      console.error('Exiting application due to database connection failure.');
      process.exit(1);
    } else {
      console.log('Running in test environment, continuing without MongoDB connection check.');
    }
  }

  // We've already handled the MongoDB connection check above

  // Setup routes
  console.log('Setting up API routes...');
  app.use('/api/users', userRoutes);
  app.use('/api/events', eventRoutes);
  app.use('/api/tasks', taskRoutes);
  app.use('/api/templates', templateRoutes);
  app.use('/api/resources', resourceRoutes);
  console.log('Resource routes set up at /api/resources');
  app.use('/api/share', shareRoutes);
  app.use('/api/health', healthRoutes);
  app.use('/api/language', languageRoutes);
  console.log('Language routes set up at /api/language');

  // Log all registered routes for debugging
  console.log('Registered routes:');
  app._router.stack.forEach(middleware => {
    if (middleware.route) {
      // Routes registered directly on the app
      console.log(`${middleware.route.path}`);
    } else if (middleware.name === 'router') {
      // Router middleware
      middleware.handle.stack.forEach(handler => {
        if (handler.route) {
          const path = handler.route.path;
          const methods = Object.keys(handler.route.methods).join(', ').toUpperCase();
          console.log(`${methods} ${middleware.regexp} ${path}`);
        }
      });
    }
  });

  if (isConnected) {
    // Check if we should seed the database
    const shouldSeed = processArgs();
    if (shouldSeed) {
      // Wrap in an async IIFE (Immediately Invoked Function Expression)
      (async () => {
        try {
          await clearDatabase();
          await seedDatabase();
          console.log('Database has been reset and seeded with fresh data.');
        } catch (error) {
          console.error('Error seeding database:', error.message);
        }
      })();
    }
  }

  // Setup error handling middlewares
  app.use(notFound);
  app.use(errorHandler);

  // Graceful shutdown with improved signal handling
  let isShuttingDown = false;

  const gracefulShutdown = (signal) => {
    if (isShuttingDown) {
      console.log('Shutdown already in progress...');
      return;
    }

    isShuttingDown = true;
    console.log(`Received ${signal}, closing connections gracefully...`);

    // Stop accepting new connections
    server.close((err) => {
      if (err) {
        console.error('Error closing HTTP server:', err);
      } else {
        console.log('HTTP server closed.');
      }

      // Close MongoDB connection if it's connected
      if (mongoose.connection.readyState === 1) {
        mongoose.connection.close()
          .then(() => {
            console.log('MongoDB connection closed.');
            process.exit(0);
          })
          .catch((err) => {
            console.error('Error closing MongoDB connection:', err);
            process.exit(1);
          });
      } else {
        console.log('No MongoDB connection to close.');
        process.exit(0);
      }
    });

    // Force exit after 10 seconds if graceful shutdown fails
    setTimeout(() => {
      console.error('Force closing after timeout...');
      process.exit(1);
    }, 10000);
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart signal

  // Handle uncaught exceptions and unhandled rejections
  process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    gracefulShutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('unhandledRejection');
  });

  // Return server instance for testing
  return server;
};

// Only start the server if this file is the main module (not imported by tests)
if (require.main === module) {
  // Initialize the server
  startServer();
}

// Export the app for testing
module.exports = { app };