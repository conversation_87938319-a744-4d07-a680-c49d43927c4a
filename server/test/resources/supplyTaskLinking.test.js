const User = require('../../models/User');
const Event = require('../../models/Event');
const Task = require('../../models/Task');
const Supply = require('../../models/resources/Supply');

describe('Supply-Task Linking', () => {
  let testUser;
  let testEvent;

  beforeAll(async () => {
    // Create a test user
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Create a test event
    testEvent = await Event.create({
      name: 'Test Event',
      title: 'Test Event Title',
      eventType: 'Other',
      date: new Date(),
      owner: testUser._id,
      collaborators: []
    });
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({ email: '<EMAIL>' });
    await Event.deleteMany({ name: 'Test Event' });
    await Task.deleteMany({ name: { $regex: /Test Task/ } });
    await Supply.deleteMany({ name: { $regex: /Test Supply/ } });
  });

  describe('Task Creation with Supplies', () => {
    it('should create a task with supplies and link them properly', async () => {
      // First create a supply
      const supply = await Supply.create({
        name: 'Test Supply for Task Creation',
        description: 'A test supply',
        quantity: 5,
        unit: 'pieces',
        estimatedCost: 100,
        category: 'Other',
        event: testEvent._id,
        tasks: [],
        createdBy: testUser._id,
        updatedBy: testUser._id
      });

      // Now create a task with this supply
      const task = await Task.create({
        name: 'Test Task with Supply',
        taskType: 'Other',
        status: 'Not Started',
        event: testEvent._id,
        supplies: [supply._id]
      });

      // Simulate the backend logic that should link supplies to tasks
      // This is what our fix should do automatically
      if (!supply.tasks.includes(task._id)) {
        supply.tasks.push(task._id);
        await supply.save();
      }

      // Verify the task has the supply
      expect(task.supplies).toHaveLength(1);
      expect(task.supplies[0].toString()).toBe(supply._id.toString());

      // Verify the supply has the task
      const updatedSupply = await Supply.findById(supply._id);
      expect(updatedSupply.tasks).toHaveLength(1);
      expect(updatedSupply.tasks[0].toString()).toBe(task._id.toString());
    });

    it('should update task supplies and maintain proper linking', async () => {
      // Create two supplies
      const supply1 = await Supply.create({
        name: 'Test Supply 1 for Update',
        description: 'First test supply',
        quantity: 3,
        unit: 'pieces',
        estimatedCost: 50,
        category: 'Other',
        event: testEvent._id,
        tasks: [],
        createdBy: testUser._id,
        updatedBy: testUser._id
      });

      const supply2 = await Supply.create({
        name: 'Test Supply 2 for Update',
        description: 'Second test supply',
        quantity: 2,
        unit: 'pieces',
        estimatedCost: 75,
        category: 'Other',
        event: testEvent._id,
        tasks: [],
        createdBy: testUser._id,
        updatedBy: testUser._id
      });

      // Create a task with the first supply
      const task = await Task.create({
        name: 'Test Task for Supply Update',
        taskType: 'Other',
        status: 'Not Started',
        event: testEvent._id,
        supplies: [supply1._id]
      });

      // Link the first supply to the task
      supply1.tasks.push(task._id);
      await supply1.save();

      // Simulate updating the task to use the second supply instead
      // This simulates what our backend fix should do
      const originalSupplyIds = [supply1._id.toString()];
      const newSupplyIds = [supply2._id.toString()];

      // Find supplies to remove from this task
      const suppliesToRemove = originalSupplyIds.filter(id => !newSupplyIds.includes(id));

      // Find supplies to add to this task
      const suppliesToAdd = newSupplyIds.filter(id => !originalSupplyIds.includes(id));

      // Remove this task from supplies that are no longer associated
      for (const supplyId of suppliesToRemove) {
        const supply = await Supply.findById(supplyId);
        if (supply) {
          supply.tasks = supply.tasks.filter(taskId => taskId.toString() !== task._id.toString());
          await supply.save();
        }
      }

      // Add this task to new supplies
      for (const supplyId of suppliesToAdd) {
        const supply = await Supply.findById(supplyId);
        if (supply && !supply.tasks.includes(task._id)) {
          supply.tasks.push(task._id);
          await supply.save();
        }
      }

      // Update the task
      task.supplies = [supply2._id];
      await task.save();

      // Verify the first supply no longer has the task
      const updatedSupply1 = await Supply.findById(supply1._id);
      expect(updatedSupply1.tasks).toHaveLength(0);

      // Verify the second supply now has the task
      const updatedSupply2 = await Supply.findById(supply2._id);
      expect(updatedSupply2.tasks).toHaveLength(1);
      expect(updatedSupply2.tasks[0].toString()).toBe(task._id.toString());
    });
  });
});
