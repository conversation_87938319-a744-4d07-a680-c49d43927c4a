const mongoose = require('mongoose');
const config = require('../config/config');

// Import models
const User = require('../models/User');
const Event = require('../models/Event');
const Task = require('../models/Task');
const Supply = require('../models/resources/Supply');

async function checkDatabase() {
  try {
    console.log('Connecting to MongoDB...');
    console.log('Database URI:', config.mongoURI);
    
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('Connected successfully!\n');
    
    // Check Users
    const userCount = await User.countDocuments();
    console.log(`Users: ${userCount} documents`);
    if (userCount > 0) {
      const users = await User.find({}, 'name email').limit(5);
      users.forEach(user => console.log(`  - ${user.name} (${user.email})`));
      if (userCount > 5) console.log(`  ... and ${userCount - 5} more`);
    }
    console.log();
    
    // Check Events
    const eventCount = await Event.countDocuments();
    console.log(`Events: ${eventCount} documents`);
    if (eventCount > 0) {
      const events = await Event.find({}, 'name title date').limit(5);
      events.forEach(event => console.log(`  - ${event.name || event.title} (${event.date})`));
      if (eventCount > 5) console.log(`  ... and ${eventCount - 5} more`);
    }
    console.log();
    
    // Check Tasks
    const taskCount = await Task.countDocuments();
    console.log(`Tasks: ${taskCount} documents`);
    if (taskCount > 0) {
      const tasks = await Task.find({}, 'name taskType status').limit(5);
      tasks.forEach(task => console.log(`  - ${task.name} (${task.taskType}, ${task.status})`));
      if (taskCount > 5) console.log(`  ... and ${taskCount - 5} more`);
    }
    console.log();
    
    // Check Supplies
    const supplyCount = await Supply.countDocuments();
    console.log(`Supplies: ${supplyCount} documents`);
    if (supplyCount > 0) {
      const supplies = await Supply.find({}, 'name category quantity').limit(5);
      supplies.forEach(supply => console.log(`  - ${supply.name} (${supply.category}, ${supply.quantity})`));
      if (supplyCount > 5) console.log(`  ... and ${supplyCount - 5} more`);
    }
    console.log();
    
    // Check all collections in the database
    console.log('All collections in database:');
    const collections = await mongoose.connection.db.listCollections().toArray();
    collections.forEach(collection => {
      console.log(`  - ${collection.name}`);
    });
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
  }
}

checkDatabase();
