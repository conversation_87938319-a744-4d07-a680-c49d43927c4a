// Centralized status color mapping for tasks

export function getStatusColor(status) {
  switch ((status || '').toLowerCase()) {
    case 'not started':
      return 'warning'; // yellow
    case 'in progress':
      return 'info'; // blue
    case 'delayed':
      return 'orange'; // orange (custom, fallback to 'warning' if not supported)
    case 'completed':
      return 'success'; // green
    case 'cancelled':
      return 'default'; // grey
    default:
      return 'default'; // grey
  }
}

// For MUI Chip, 'warning' is yellow, 'info' is blue, 'success' is green, 'default' is grey.
// For 'orange', you may need to use sx={{ bgcolor: 'orange' }} if your component doesn't support 'orange' directly.
