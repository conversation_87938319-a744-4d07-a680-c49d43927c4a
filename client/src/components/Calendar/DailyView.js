import html2canvas from 'html2canvas';
// Centralized status color mapping
const STATUS_COLORS = {
  'Not Started': 'yellow', // yellow
  'In Progress': 'blue',  // blue
  'Delayed': 'orange',    // orange
  'Completed': 'green',   // green
  'Cancelled': 'grey'     // grey
};

// Centralized MUI color mapping for Chips
const STATUS_CHIP_COLORS = {
  'Not Started': 'warning', // yellow
  'In Progress': 'info',   // blue
  'Delayed': 'warning',    // orange (MUI warning is orange)
  'Completed': 'success',  // green
  'Cancelled': 'default'   // grey
};

// Centralized function for Chip color
function getStatusChipColor(status) {
  return STATUS_CHIP_COLORS[status] || 'default';
}

// Centralized function for background color (for ListItem, etc)
function getStatusBgColor(status, theme) {
  switch (status) {
    case 'Not Started': return '#fffde7'; // yellow 50
    case 'In Progress': return '#e3f2fd'; // blue 50
    case 'Delayed': return '#ffe0b2';     // orange 100
    case 'Completed': return '#e8f5e9';   // green 50
    case 'Cancelled': return '#f5f5f5';   // grey 100
    default: return theme.palette.grey[100];
  }
}

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Grid,
  IconButton,
  Divider,
  Chip,
  Button,
  Tooltip,
  useTheme,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  AvatarGroup,
  useMediaQuery,
  Menu,
  MenuItem
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import TodayIcon from '@mui/icons-material/Today';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import EventIcon from '@mui/icons-material/Event';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';
import UnfoldLessIcon from '@mui/icons-material/UnfoldLess';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import * as XLSX from 'xlsx';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DragHandleIcon from '@mui/icons-material/DragHandle';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { format, addDays, subDays, startOfDay, addHours, isSameDay, getDaysInMonth, getDay } from 'date-fns';
import TaskForm from '../TaskForm';
import CloseIcon from '@mui/icons-material/Close';
import { updateTask } from '../../services/taskService';

// Utility function to detect overlapping tasks
const findOverlappingTasks = (tasks) => {
  const groups = [];
  
  tasks.forEach(task => {
    if (!task.startTime) return;
    
    const taskStart = new Date(task.startTime);
    let taskEnd = null;
    
    // Calculate task end time
    if (task.duration && task.duration !== '00:00:00') {
      const [hours, minutes, seconds] = task.duration.split(':').map(Number);
      taskEnd = new Date(taskStart);
      taskEnd.setHours(taskEnd.getHours() + hours);
      taskEnd.setMinutes(taskEnd.getMinutes() + minutes);
      taskEnd.setSeconds(taskEnd.getSeconds() + seconds);
    } else if (task.softDeadline) {
      taskEnd = new Date(task.softDeadline);
    } else if (task.hardDeadline) {
      taskEnd = new Date(task.hardDeadline);
    } else {
      // Default 1-hour duration
      taskEnd = new Date(taskStart.getTime() + 60 * 60 * 1000);
    }
    
    // Find existing group that overlaps with this task
    let foundGroup = null;
    for (let group of groups) {
      for (let existingTask of group) {
        if (!existingTask.startTime) continue;
        
        const existingStart = new Date(existingTask.startTime);
        let existingEnd = null;
        
        if (existingTask.duration && existingTask.duration !== '00:00:00') {
          const [hours, minutes, seconds] = existingTask.duration.split(':').map(Number);
          existingEnd = new Date(existingStart);
          existingEnd.setHours(existingEnd.getHours() + hours);
          existingEnd.setMinutes(existingEnd.getMinutes() + minutes);
          existingEnd.setSeconds(existingEnd.getSeconds() + seconds);
        } else if (existingTask.softDeadline) {
          existingEnd = new Date(existingTask.softDeadline);
        } else if (existingTask.hardDeadline) {
          existingEnd = new Date(existingTask.hardDeadline);
        } else {
          existingEnd = new Date(existingStart.getTime() + 60 * 60 * 1000);
        }
        
        // Check for overlap
        if (taskStart < existingEnd && taskEnd > existingStart) {
          foundGroup = group;
          break;
        }
      }
      if (foundGroup) break;
    }
    
    if (foundGroup) {
      foundGroup.push(task);
    } else {
      groups.push([task]);
    }
  });
  
  return groups;
};

// Utility function to detect overlapping tasks and assign columns (Google Calendar style)
const assignTaskColumns = (tasks) => {
  // Sort tasks by start time
  const sortedTasks = [...tasks].sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
  // Each entry: { task, start, end, column, columnsCount }
  const entries = [];
  // Track columns in use at each time
  let active = [];

  sortedTasks.forEach(task => {
    if (!task.startTime) return;
    const start = new Date(task.startTime).getTime();
    let end = null;
    if (task.duration && task.duration !== '00:00:00') {
      const [h, m, s] = task.duration.split(':').map(Number);
      end = start + h * 3600000 + m * 60000 + s * 1000;
    } else if (task.softDeadline) {
      end = new Date(task.softDeadline).getTime();
    } else if (task.hardDeadline) {
      end = new Date(task.hardDeadline).getTime();
    } else {
      end = start + 3600000;
    }
    // Remove finished tasks from active
    active = active.filter(e => e.end > start);
    // Find first available column
    let col = 0;
    const usedCols = active.map(e => e.column);
    while (usedCols.includes(col)) col++;
    // Add to active
    const entry = { task, start, end, column: col };
    active.push(entry);
    entries.push(entry);
  });

  // For each entry, find max columns overlapping during its time
  entries.forEach(entry => {
    entry.columnsCount = entries.filter(e =>
      (e.start < entry.end && e.end > entry.start)
    ).reduce((max, e) => Math.max(max, e.column + 1), 1);
  });

  // Group by overlap (same as before)
  const groups = [];
  entries.forEach(entry => {
    let foundGroup = null;
    for (let group of groups) {
      if (group.some(e => e.start < entry.end && e.end > entry.start)) {
        foundGroup = group;
        break;
      }
    }
    if (foundGroup) {
      foundGroup.push(entry);
    } else {
      groups.push([entry]);
    }
  });
  return groups;
};

// Memoized components to prevent unnecessary re-renders
const TimeSlot = React.memo(({ time, hourHeight, index, timeSlots, onCreateTask, assigneeId, theme }) => {
  const handleClick = useCallback(() => {
    onCreateTask(assigneeId, time);
  }, [onCreateTask, assigneeId, time]);

  const handleAddClick = useCallback((e) => {
    e.stopPropagation();
    onCreateTask(assigneeId, time);
  }, [onCreateTask, assigneeId, time]);

  return (
    <Box
      sx={{
        height: hourHeight,
        borderBottom: index < timeSlots.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
        position: 'relative'
      }}
      onClick={handleClick}
    >
      <IconButton
        size="small"
        sx={{
          position: 'absolute',
          top: 2,
          right: 2,
          opacity: 0.3,
          '&:hover': { opacity: 1 }
        }}
        onClick={handleAddClick}
      >
        <AddIcon fontSize="small" />
      </IconButton>
    </Box>
  );
});

const TaskItem = React.memo(({
  task,
  position,
  color,
  theme,
  isDragging,
  draggedTask,
  dragType,
  taskIndex,
  totalOverlapping,
  isInCollapsedGroup,
  t,
  onTaskMouseDown,
  onEditTask,
  isMobile,
  onMobileTooltipOpen,
  left,
  width
}) => {
  const { top, height } = position;
  
  // Calculate horizontal positioning for overlapping tasks
  const getHorizontalPosition = () => {
    if (totalOverlapping <= 1) {
      return { left: '4px', width: 'calc(100% - 8px)' };
    }
    
    // Google Calendar style positioning
    const baseLeftMargin = 4;
    const baseRightMargin = 8;
    const overlapOffset = 2; // Small offset between overlapping tasks
    
    const availableWidth = `calc(100% - ${baseLeftMargin + baseRightMargin}px)`;
    const taskWidth = `calc((${availableWidth}) / ${totalOverlapping} - ${overlapOffset * (totalOverlapping - 1)}px / ${totalOverlapping})`;
    const leftPosition = `calc(${baseLeftMargin}px + (${availableWidth}) * ${taskIndex} / ${totalOverlapping} + ${overlapOffset * taskIndex}px)`;
    
    return {
      left: leftPosition,
      width: taskWidth
    };
  };
  
  const horizontalPos = left && width ? { left, width } : getHorizontalPosition();
  
  const handleMouseDown = useCallback((e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const y = e.clientY - rect.top;

    e.preventDefault();
    e.stopPropagation();

    if (y < 10) {
      onTaskMouseDown(task, e, 'resize-top');
    } else if (y > rect.height - 10) {
      onTaskMouseDown(task, e, 'resize-bottom');
    } else {
      onTaskMouseDown(task, e, 'move');
    }
  }, [task, onTaskMouseDown]);

  const handleEditClick = useCallback((e) => {
    e.stopPropagation();
    onEditTask(task);
  }, [task, onEditTask]);

  const handleDoubleClick = useCallback((e) => {
    e.stopPropagation();
    onEditTask(task);
  }, [task, onEditTask]);

  const handleClick = useCallback((e) => {
    // Only handle single click on mobile for tooltip
    if (isMobile) {
      e.stopPropagation();
      // Check if this is part of a double-click sequence
      // We'll use a simple timeout to distinguish single vs double click
      if (task._clickTimeout) {
        clearTimeout(task._clickTimeout);
        task._clickTimeout = null;
        // This is a double click, let the double click handler take over
        return;
      }

      task._clickTimeout = setTimeout(() => {
        task._clickTimeout = null;
        // This is a single click, show mobile tooltip
        if (onMobileTooltipOpen) {
          onMobileTooltipOpen(task);
        }
      }, 200); // 200ms delay to detect double click
    }
  }, [task, isMobile, onMobileTooltipOpen]);

  // Memoize tooltip content
  const tooltipContent = useMemo(() => (
    <Box sx={{ p: 0.5, maxWidth: 250 }}>
      <Typography variant="subtitle2" fontWeight="bold">
        {task.name}
      </Typography>

      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
        <Box
          sx={{
            width: 10,
            height: 10,
            borderRadius: '50%',
            mr: 1,
            bgcolor:
              task.status === 'Completed' ? 'success.main' :
              task.status === 'In Progress' ? 'info.main' :
              task.status === 'Delayed' ? 'warning.main' :
              task.status === 'Cancelled' ? 'error.main' :
              'action.disabled'
          }}
        />
        <Typography variant="caption">
          {task.status}
        </Typography>
      </Box>

      {task.startTime && (
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
          <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
          <Typography variant="caption">
            {t('calendar.dailyView.startTime')}: {format(new Date(task.startTime), 'h:mm a')}
          </Typography>
        </Box>
      )}

      {task.startTime && task.duration && task.duration !== '00:00:00' && (
        <>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
            <Typography variant="caption">
              {t('taskForm.duration', 'Duration')}: {task.duration}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
            <Typography variant="caption">
              {t('taskForm.end', 'End')}: {(() => {
                try {
                  const startTime = new Date(task.startTime);
                  const [hours, minutes, seconds] = task.duration.split(':').map(Number);
                  const endTime = new Date(startTime);
                  endTime.setHours(endTime.getHours() + hours);
                  endTime.setMinutes(endTime.getMinutes() + minutes);
                  endTime.setSeconds(endTime.getSeconds() + seconds);
                  return format(endTime, 'h:mm a');
                } catch (error) {
                  return '';
                }
              })()}
            </Typography>
          </Box>
        </>
      )}

      {task.location && (
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
          <LocationOnIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
          <Typography variant="caption">
            {task.location}
          </Typography>
        </Box>
      )}

      {task.details && (
        <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary' }}>
          {task.details.length > 100 ? `${task.details.substring(0, 100)}...` : task.details}
        </Typography>
      )}
    </Box>
  ), [task, t]);

  // Memoize assignee avatars
  const assigneeAvatars = useMemo(() => {
    if (!isInCollapsedGroup || !task.assignees || task.assignees.length === 0) return null;

    return (
      <AvatarGroup max={3} sx={{
        position: 'absolute',
        top: 1,
        right: 1,
        '& .MuiAvatar-root': {
          width: 14,
          height: 14,
          fontSize: '0.5rem',
          border: '1px solid white'
        }
      }}>
        {task.assignees.map((assignee, index) => (
          <Avatar 
            key={index}
            src={assignee.avatar}
            sx={{ 
              bgcolor: assignee.isNonUserAssignee ? theme.palette.secondary.main : theme.palette.primary.main,
            }}
          >
            {assignee.name ? assignee.name.charAt(0).toUpperCase() : '?'}
          </Avatar>
        ))}
      </AvatarGroup>
    );
  }, [isInCollapsedGroup, task.assignees, theme]);

  // Calculate z-index based on overlap position
  const zIndex = useMemo(() => {
    if ((isDragging || draggedTask?._isPreparing) && draggedTask?._id === task._id) return 1000;
    if (totalOverlapping > 1) return 10 + taskIndex;
    return 1;
  }, [isDragging, draggedTask, task._id, totalOverlapping, taskIndex]);

  return (
    <Tooltip
      title={tooltipContent}
      arrow
      placement="top"
      enterDelay={500}
      leaveDelay={0}
      disableInteractive
    >
      <Paper
        elevation={2}
        data-task-id={task._id}
        sx={{
          position: 'absolute',
          top: `${top}px`,
          left: horizontalPos.left,
          width: horizontalPos.width,
          height: `${height}px`,
          backgroundColor: color,
          color: theme.palette.getContrastText(color),
          p: '0px', // Set padding for the whole task item to 2px
          overflow: 'hidden',
          cursor: (isDragging || draggedTask?._isPreparing) && draggedTask?._id === task._id ?
            (dragType === 'move' ? 'grabbing' :
            dragType === 'resize-top' ? 'row-resize' :
            dragType === 'resize-bottom' ? 'row-resize' :
            draggedTask?._dragType === 'move' ? 'grab' :
            draggedTask?._dragType === 'resize-top' ? 'row-resize' :
            draggedTask?._dragType === 'resize-bottom' ? 'row-resize' : 'pointer') : 'pointer',
          zIndex: zIndex,
          display: 'flex',
          flexDirection: 'column',
          border: totalOverlapping > 1 ? '1px solid rgba(255,255,255,0.3)' : 'none',
          borderTop: '1px solid #333', // Add a dark grey top border for visual start
          '&:hover': {
            opacity: 0.9,
            boxShadow: theme.shadows[4],
            zIndex: isDragging && draggedTask?._id === task._id ? 1000 : 999
          },
          '&:hover::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '10px',
            cursor: 'row-resize',
            backgroundColor: 'rgba(0,0,0,0.1)'
          },
          '&:hover::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '10px',
            cursor: 'row-resize',
            backgroundColor: 'rgba(0,0,0,0.1)'
          }
        }}
        onMouseDown={handleMouseDown}
        onDoubleClick={handleDoubleClick}
        onClick={isMobile ? handleClick : undefined}
      >
        {assigneeAvatars}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: '0px', p: '0px' }}>
          <Typography
            variant={totalOverlapping > 2 ? "caption" : "body2"}
            noWrap
            sx={{
              flex: 1,
              pr: isInCollapsedGroup ? 0 : 1, // Reduce right padding to max 2px
              fontSize: totalOverlapping > 3 ? '0.5rem' : totalOverlapping > 2 ? '0.6rem' : '0.65rem',
              lineHeight: 1.0,
              minHeight: '1em', // Ensure at least one line is visible
              maxHeight: '1.2em', // Prevent overflow
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              m: '0px', // Remove margin
              p: '0px' // Remove padding
            }}
          >
            {(() => {
              // Show start time on same line if task name is not too long and there's space
              const taskName = task.name || '';
              let startTime = '';

              // Safely format start time
              if (task.startTime) {
                try {
                  startTime = format(new Date(task.startTime), 'h:mm a');
                } catch (error) {
                  startTime = '';
                }
              }

              // Only show time inline if:
              // 1. Task has start time
              // 2. Task name is reasonably short (less than 25 characters for better readability)
              // 3. Not too many overlapping tasks (to preserve space)
              // 4. Not in collapsed group (to preserve space)
              if (startTime &&
                  taskName.length <= 25 &&
                  totalOverlapping <= 3 &&
                  !isInCollapsedGroup) {
                return `${taskName} (${startTime})`;
              }

              return taskName;
            })()}
          </Typography>
          {!isInCollapsedGroup && (
            <IconButton
              size="small"
              sx={{
                p: '2px',
                ml: 0.25,
                minWidth: totalOverlapping > 2 ? 14 : 20,
                width: totalOverlapping > 2 ? 14 : 20,
                height: totalOverlapping > 2 ? 14 : 20,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.5)' }
              }}
              onClick={handleEditClick}
            >
              <EditIcon fontSize={totalOverlapping > 2 ? "inherit" : "small"} sx={{ fontSize: totalOverlapping > 2 ? '10px' : '14px' }} />
            </IconButton>
          )}
        </Box>

        {height >= 45 && !isInCollapsedGroup && totalOverlapping <= 3 && (
          <>
            {task.duration && task.duration !== '00:00:00' && (
              <Typography variant="caption" noWrap sx={{ mt: 0.25, fontSize: '0.55rem', lineHeight: 0.9 }}>
                {t('taskForm.duration', 'Duration')}: {task.duration}
              </Typography>
            )}

            {task.startTime && task.duration && task.duration !== '00:00:00' && (
              <Typography variant="caption" noWrap sx={{ fontSize: '0.55rem', lineHeight: 0.9 }}>
                {t('taskForm.end', 'End')}: {(() => {
                  try {
                    const startTime = new Date(task.startTime);
                    const [hours, minutes, seconds] = task.duration.split(':').map(Number);
                    const endTime = new Date(startTime);
                    endTime.setHours(endTime.getHours() + hours);
                    endTime.setMinutes(endTime.getMinutes() + minutes);
                    endTime.setSeconds(endTime.getSeconds() + seconds);
                    return format(endTime, 'h:mm a');
                  } catch (error) {
                    return '';
                  }
                })()}
              </Typography>
            )}

            {task.location && (
              <Typography variant="caption" noWrap sx={{ fontSize: '0.55rem', lineHeight: 0.9 }}>
                {task.location}
              </Typography>
            )}

            {task.taskType && !task.location && (
              <Typography variant="caption" noWrap sx={{ fontSize: '0.55rem', lineHeight: 0.9 }}>
                {task.taskType}
              </Typography>
            )}
          </>
        )}
      </Paper>
    </Tooltip>
  );
});

const AssigneeColumn = React.memo(({
  assignee,
  timeSlots,
  hourHeight,
  assigneeTasks,
  filteredAssignees,
  onCreateTask,
  theme,
  t,
  isDragging,
  draggedTask,
  dragType,
  onTaskMouseDown,
  onEditTask,
  currentDate,
  zoomLevel,
  getTaskColor,
  columnSizing,
  isMobile,
  onMobileTooltipOpen
}) => {
  // Calculate task position
  const getTaskPosition = useCallback((task) => {
    const startTime = task.startTime ? new Date(task.startTime) : null;

    if (!startTime) return { top: 0, height: hourHeight };

    const startHour = startTime.getHours();
    const startMinutes = startTime.getMinutes();
    const top = (startHour * 60 + startMinutes) * zoomLevel;

    let height = hourHeight;

    if (task.duration && task.duration !== '00:00:00') {
      try {
        const [hours, minutes] = task.duration.split(':').map(Number);
        height = ((hours * 60) + minutes) * zoomLevel;
        const minHeight = 16 * zoomLevel; // Lowered minimum height
        if (height < minHeight) height = minHeight;
      } catch (error) {
        console.error('Error calculating task height from duration:', error);
        height = hourHeight;
      }
    } else if (task.softDeadline || task.hardDeadline) {
      const endTime = task.softDeadline ? new Date(task.softDeadline) :
                    (task.hardDeadline ? new Date(task.hardDeadline) : null);

      if (endTime) {
        const endHour = endTime.getHours();
        const endMinutes = endTime.getMinutes();
        const endPosition = (endHour * 60 + endMinutes) * zoomLevel;
        height = endPosition - top;
        const minHeight = 16 * zoomLevel; // Lowered minimum height
        if (height < minHeight) height = minHeight;
      }
    }

    return { top, height };
  }, [hourHeight, zoomLevel]);

  // Find overlapping task groups
  const overlappingGroups = useMemo(() => {
    return assignTaskColumns(assigneeTasks);
  }, [assigneeTasks]);

  // Memoize task rendering to prevent recalculation on every render
  const renderedTasks = useMemo(() => {
    const taskElements = [];
    
    overlappingGroups.forEach(group => {
      group.forEach(entry => {
        const { task, column, columnsCount } = entry;
        const position = getTaskPosition(task);
        const color = getTaskColor(task);
        const isInCollapsedGroup = assignee.isGroup && !assignee.isExpanded;

        // Calculate horizontal position and width
        const baseLeftMargin = 4;
        const baseRightMargin = 8;
        const overlapOffset = 2;
        const availableWidth = `calc(100% - ${baseLeftMargin + baseRightMargin}px)`;
        const taskWidth = `calc((${availableWidth}) / ${columnsCount} - ${overlapOffset * (columnsCount - 1)}px / ${columnsCount})`;
        const leftPosition = `calc(${baseLeftMargin}px + (${availableWidth}) * ${column} / ${columnsCount} + ${overlapOffset * column}px)`;

        taskElements.push(
          <TaskItem
            key={task._id}
            task={task}
            position={position}
            color={color}
            theme={theme}
            isDragging={isDragging}
            draggedTask={draggedTask}
            dragType={dragType}
            overlappingGroup={group.map(e => e.task)}
            taskIndex={column}
            totalOverlapping={columnsCount}
            isInCollapsedGroup={isInCollapsedGroup}
            t={t}
            onTaskMouseDown={onTaskMouseDown}
            onEditTask={onEditTask}
            currentDate={currentDate}
            zoomLevel={zoomLevel}
            isMobile={isMobile}
            onMobileTooltipOpen={onMobileTooltipOpen}
            left={leftPosition}
            width={taskWidth}
          />
        );
      });
    });

    return taskElements;
  }, [overlappingGroups, getTaskPosition, getTaskColor, assignee.isGroup, assignee.isExpanded, theme, isDragging, draggedTask, dragType, t, onTaskMouseDown, onEditTask, currentDate, zoomLevel, isMobile, onMobileTooltipOpen]);

  return (
    <Box
      sx={{
        ...columnSizing,
        borderRight: `1px solid ${theme.palette.divider}`,
        position: 'relative'
      }}
    >
      {timeSlots.map((time, index) => (
        <TimeSlot
          key={index}
          time={time}
          hourHeight={hourHeight}
          index={index}
          timeSlots={timeSlots}
          onCreateTask={onCreateTask}
          assigneeId={assignee._id}
          theme={theme}
        />
      ))}
      {renderedTasks}
    </Box>
  );
});

const DailyView = ({ selectedDate, events = [], tasks = [], users = [], onTaskCreate, onTaskUpdate, onTaskDelete }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Initialize all state hooks first to ensure consistent hook order
  const [currentDate, setCurrentDate] = useState(() => selectedDate || new Date());
  const [currentTime, setCurrentTime] = useState(new Date());
  const [zoomLevel, setZoomLevel] = useState(1);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Configurable column width state
  const [columnWidth, setColumnWidth] = useState(() => {
    try {
      const savedWidth = localStorage.getItem('dailyViewColumnWidth');
      return savedWidth ? JSON.parse(savedWidth) : {
        mobile: 160,
        desktop: 250
      };
    } catch (error) {
      console.error('Error loading column width from localStorage:', error);
      return {
        mobile: 160,
        desktop: 250
      };
    }
  });
  const [columnOrder, setColumnOrder] = useState(() => {
    try {
      const savedOrder = localStorage.getItem('dailyViewColumnOrder');
      return savedOrder ? JSON.parse(savedOrder) : [];
    } catch (error) {
      console.error('Error loading column order from localStorage:', error);
      return [];
    }
  });
  const [isColumnDragging, setIsColumnDragging] = useState(false);
  const [draggedColumnId, setDraggedColumnId] = useState(null);
  const [draggedColumnIndex, setDraggedColumnIndex] = useState(-1);
  const [dropIndicatorIndex, setDropIndicatorIndex] = useState(-1);
  const [selectedTask, setSelectedTask] = useState(null);
  const [openTaskDialog, setOpenTaskDialog] = useState(false);
  const [openMonthlyDialog, setOpenMonthlyDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  // Mobile tooltip dialog state
  const [mobileTooltipOpen, setMobileTooltipOpen] = useState(false);
  const [mobileTooltipTask, setMobileTooltipTask] = useState(null);
  const [assigneeGroups, setAssigneeGroups] = useState(() => {
    try {
      const savedGroups = localStorage.getItem('dailyViewAssigneeGroups');
      return savedGroups ? JSON.parse(savedGroups) : [];
    } catch (error) {
      console.error('Error loading assignee groups from localStorage:', error);
      return [];
    }
  });
  const [openGroupDialog, setOpenGroupDialog] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [newGroupName, setNewGroupName] = useState('');
  const [groupMemberIds, setGroupMemberIds] = useState([]);
  const [mobileMenuAnchor, setMobileMenuAnchor] = useState(null);
  const [expandedGroups, setExpandedGroups] = useState(() => {
    try {
      const savedExpandedGroups = localStorage.getItem('dailyViewExpandedGroups');
      return savedExpandedGroups ? JSON.parse(savedExpandedGroups) : {};
    } catch (error) {
      console.error('Error loading expanded groups from localStorage:', error);
      return {};
    }
  });
  const [isDragging, setIsDragging] = useState(false);
  const [draggedTask, setDraggedTask] = useState(null);
  const [dragType, setDragType] = useState(null);

  const [dragInfo, setDragInfo] = useState(null);
  const [, setJustFinishedDragging] = useState(false);

  // Add export to image handler
  const exportDailyViewToImage = useCallback(() => {
    // For mobile, capture the scrollable content as well
    const dailyViewElement = document.querySelector('[data-daily-view-root]');
    if (!dailyViewElement) return;
    // Temporarily expand scrollable area for full capture
    const scrollBox = dailyViewElement.querySelector('[data-daily-scroll-box]');
    const headerScrollBox = dailyViewElement.querySelector('[data-daily-header-scroll-box]');
    let originalScrollStyles = {};
    let originalHeaderStyles = {};
    if (scrollBox) {
      originalScrollStyles = {
        height: scrollBox.style.height,
        maxHeight: scrollBox.style.maxHeight,
        overflow: scrollBox.style.overflow,
      };
      scrollBox.style.height = 'auto';
      scrollBox.style.maxHeight = 'none';
      scrollBox.style.overflow = 'visible';
    }
    if (headerScrollBox) {
      originalHeaderStyles = {
        height: headerScrollBox.style.height,
        maxHeight: headerScrollBox.style.maxHeight,
        overflow: headerScrollBox.style.overflow,
      };
      headerScrollBox.style.height = 'auto';
      headerScrollBox.style.maxHeight = 'none';
      headerScrollBox.style.overflow = 'visible';
    }
    html2canvas(dailyViewElement, {
      useCORS: true,
      backgroundColor: '#fff',
      windowWidth: dailyViewElement.scrollWidth,
      windowHeight: dailyViewElement.scrollHeight
    }).then(canvas => {
      if (scrollBox) {
        scrollBox.style.height = originalScrollStyles.height;
        scrollBox.style.maxHeight = originalScrollStyles.maxHeight;
        scrollBox.style.overflow = originalScrollStyles.overflow;
      }
      if (headerScrollBox) {
        headerScrollBox.style.height = originalHeaderStyles.height;
        headerScrollBox.style.maxHeight = originalHeaderStyles.maxHeight;
        headerScrollBox.style.overflow = originalHeaderStyles.overflow;
      }
      const link = document.createElement('a');
      link.download = `daily-view-${format(currentDate, 'yyyy-MM-dd')}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    });
  }, [currentDate]);

  // Add state for share dialog
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [shareDialogUrl, setShareDialogUrl] = useState('');

  // Add state for ongoing tasks modal
  const [ongoingTasksOpen, setOngoingTasksOpen] = useState(false);
  const [ongoingTasks, setOngoingTasks] = useState([]);
  // Default filter: Not Started, In Progress, Delayed
  const [ongoingTasksStatusFilter, setOngoingTasksStatusFilter] = useState(['Not Started', 'In Progress', 'Delayed']); // Array of statuses to filter
  const TASK_STATUSES = ['Not Started', 'In Progress', 'Completed', 'Delayed', 'Cancelled'];

  // Initialize refs
  const scrollContainerRef = useRef(null);
  const headerScrollRef = useRef(null);

  // Memoized values
  const hourHeight = useMemo(() => 60 * zoomLevel, [zoomLevel]);

  // Detect if viewing via share token
  const shareToken = useMemo(() => {
    const match = window.location.pathname.match(/\/calendar\/shared\/([a-f0-9]{32})/);
    return match ? match[1] : null;
  }, [location.pathname]);

  // State for shared events and tasks
  const [sharedEvents, setSharedEvents] = useState([]);
  const [sharedTasks, setSharedTasks] = useState([]);
  const [sharedDate, setSharedDate] = useState(null);

  // If shareToken is present, fetch the shared day's data and tasks
  useEffect(() => {
    if (!shareToken) return;
    fetch(`/api/share/${shareToken}`)
      .then(res => res.json())
      .then(data => {
        if (data.events && data.events.length > 0) {
          setSharedEvents(data.events);
          const eventDate = new Date(data.events[0].date);
          setSharedDate(eventDate);
          setCurrentDate(eventDate);
          // Use tasks from API response
          setSharedTasks(data.tasks || []);
        } else {
          setSharedEvents([]);
          setSharedTasks([]);
          setSharedDate(null);
        }
      });
  }, [shareToken]);

  // Use sharedEvents/sharedTasks/sharedDate if present
  const eventsToShow = shareToken ? sharedEvents : events;
  const tasksToShow = shareToken ? sharedTasks : tasks;
  const dateToShow = shareToken ? sharedDate || currentDate : currentDate;

  // Update isReadOnly to also be true if shareToken is present
  const isReadOnly = useMemo(() => {
    if (shareToken) return true;
    const params = new URLSearchParams(location.search);
    return params.get('readonly') === '1';
  }, [location.search, shareToken]);

  // Handle mouse down on task for dragging
  const handleTaskMouseDown = useCallback((task, event, type = 'move') => {
    event.preventDefault();
    event.stopPropagation();

    const taskElement = event.currentTarget;
    const rect = taskElement.getBoundingClientRect();

    const initialMouseY = event.clientY;
    const initialMouseX = event.clientX;

    const computedStyle = window.getComputedStyle(taskElement);
    const initialTop = parseInt(computedStyle.top, 10);
    const initialHeight = parseInt(computedStyle.height, 10);

    const offsetY = initialMouseY - rect.top;

    // Store drag preparation data but don't start dragging yet
    setDraggedTask({
      ...task,
      _initialMouseY: initialMouseY,
      _initialMouseX: initialMouseX,
      _initialTop: initialTop,
      _initialHeight: initialHeight,
      _offsetY: offsetY,
      _dragType: type,
      _isPreparing: true // Flag to indicate we're preparing to drag but not actually dragging yet
    });

  }, []);

  // Safe handlers for read-only mode
  const safeOnTaskCreate = useCallback((...args) => {
    if (isReadOnly) return;
    onTaskCreate && onTaskCreate(...args);
  }, [isReadOnly, onTaskCreate]);

  const safeOnTaskUpdate = useCallback((...args) => {
    if (isReadOnly) return;
    onTaskUpdate && onTaskUpdate(...args);
  }, [isReadOnly, onTaskUpdate]);

  const safeOnTaskDelete = useCallback((...args) => {
    if (isReadOnly) return;
    onTaskDelete && onTaskDelete(...args);
  }, [isReadOnly, onTaskDelete]);

  // Safe mouse down handler for tasks
  const safeHandleTaskMouseDown = useCallback((task, event, type = 'move') => {
    if (isReadOnly) return;
    // Call the original handler only if not read-only
    handleTaskMouseDown(task, event, type);
  }, [isReadOnly, handleTaskMouseDown]);

  // Helper to generate and show share link
  const handleShareReadOnly = useCallback(async () => {
    const formattedDate = currentDate.toISOString().split('T')[0];
    try {
      // Call backend to generate token
      const response = await fetch('/api/share/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date: formattedDate })
      });
      const data = await response.json();
      if (!data.token) throw new Error('No token returned');
      const shareUrl = `${window.location.protocol}//${window.location.host}/calendar/shared/${data.token}`;
      setShareDialogUrl(shareUrl);
      setShareDialogOpen(true);
      // Try to copy to clipboard
      if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
        navigator.clipboard.writeText(shareUrl)
          .then(() => {
            setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopied', 'Read-only link copied to clipboard!'));
            setSnackbarSeverity('info');
            setSnackbarOpen(true);
          })
          .catch(() => {
            setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopyFailed', 'Could not copy. Please copy manually: ') + shareUrl);
            setSnackbarSeverity('warning');
            setSnackbarOpen(true);
          });
      } else {
        try {
          const tempInput = document.createElement('input');
          tempInput.value = shareUrl;
          document.body.appendChild(tempInput);
          tempInput.select();
          document.execCommand('copy');
          document.body.removeChild(tempInput);
          setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopied', 'Read-only link copied to clipboard!'));
          setSnackbarSeverity('info');
          setSnackbarOpen(true);
        } catch (err) {
          setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopyFailed', 'Could not copy. Please copy manually: ') + shareUrl);
          setSnackbarSeverity('warning');
          setSnackbarOpen(true);
        }
      }
    } catch (err) {
      setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopyFailed', 'Could not copy. Please try again.'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  }, [currentDate, t]);

  // Update currentDate when selectedDate prop changes
  useEffect(() => {
    if (selectedDate) {
      console.log('DailyView - selectedDate changed:', selectedDate);
      const newDate = new Date(selectedDate);
      setCurrentDate(newDate);
    }
  }, [selectedDate]);

  // Log the current date whenever it changes
  useEffect(() => {
    console.log('DailyView - currentDate changed:', currentDate);
  }, [currentDate]);

  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Helper functions defined before use
  const isTaskOnDay = useCallback((task, date = currentDate) => {
    if (task.startTime) {
      const taskStart = new Date(task.startTime);
      return isSameDay(taskStart, date);
    }

    if (task.softDeadline) {
      const deadline = new Date(task.softDeadline);
      return isSameDay(deadline, date);
    }

    if (task.hardDeadline) {
      const deadline = new Date(task.hardDeadline);
      return isSameDay(deadline, date);
    }

    return false;
  }, [currentDate]);

  // Memoize filtered tasks for current day to prevent recalculation
  const tasksForCurrentDay = useMemo(() => {
    if (!tasksToShow || tasksToShow.length === 0) return [];
    
    return tasksToShow.filter(task => {
      if (task.deleted) return false;
      return isTaskOnDay(task, dateToShow);
    });
  }, [tasksToShow, dateToShow, isTaskOnDay]);

  // Memoize assignees extraction
  const extractedAssignees = useMemo(() => {
    if (!tasksForCurrentDay || tasksForCurrentDay.length === 0) return [];

    const uniqueAssignees = [];
    const assigneeMap = new Map();
    const nonUserAssigneeMap = new Map();

    // Always add "Unassigned" column
    uniqueAssignees.push({
      _id: 'unassigned',
      name: t('calendar.dailyView.unassigned'),
      email: '',
      isSpecial: true
    });

    tasksForCurrentDay.forEach(task => {
      if (task.assignees && task.assignees.length > 0) {
        task.assignees.forEach(assignee => {
          if (assignee && (assignee.isNonUserAssignee || (!assignee._id && assignee.name))) {
            const name = assignee.name || 'Unknown';
            if (!nonUserAssigneeMap.has(name)) {
              const nonUserAssignee = {
                _id: `non-user-${name.replace(/\s+/g, '-').toLowerCase()}`,
                name: name,
                email: assignee.email || '',
                isNonUserAssignee: true,
                originalAssignee: assignee
              };
              nonUserAssigneeMap.set(name, nonUserAssignee);
              uniqueAssignees.push(nonUserAssignee);
            }
          } else if (assignee && assignee._id && !assigneeMap.has(assignee._id)) {
            assigneeMap.set(assignee._id, true);
            uniqueAssignees.push(assignee);
          }
        });
      }
    });

    return uniqueAssignees;
  }, [tasksForCurrentDay, t]);

  // Memoize task organization functions
  const organizeTaskHierarchy = useCallback((tasks) => {
    const nonDeletedTasks = tasks.filter(task => !task.deleted);
    const topLevelTasks = nonDeletedTasks.filter(task => !task.parentTask);
    const subtasks = nonDeletedTasks.filter(task => task.parentTask);

    return topLevelTasks.map(task => {
      const taskSubtasks = subtasks.filter(subtask =>
        subtask.parentTask === task._id
      );

      return {
        ...task,
        subtasks: taskSubtasks
      };
    });
  }, []);

  // Memoize task color calculation
  const getTaskColor = useCallback((task) => {
    switch (task.status) {
      case 'Not Started':
        return theme.palette.warning.main; // yellow
      case 'Completed':
        return theme.palette.success.main; // green
      case 'In Progress':
        return theme.palette.info.main; // blue
      case 'Delayed':
        return theme.palette.warning.main; // orange
      case 'Cancelled':
        return theme.palette.grey[400]; // grey
      default:
        if (task.startTime && isSameDay(new Date(task.startTime), dateToShow)) {
          return theme.palette.success.light;
        }
        if (task.hardDeadline && isSameDay(new Date(task.hardDeadline), dateToShow)) {
          return theme.palette.error.light;
        }
        if (task.softDeadline && isSameDay(new Date(task.softDeadline), dateToShow)) {
          return theme.palette.primary.light;
        }
        return theme.palette.grey[300];
    }
  }, [theme, dateToShow]);

  // Memoize tasks for each assignee
  const getTasksForAssignee = useCallback((assigneeId) => {
    const assigneeTasks = tasksForCurrentDay.filter(task => {
      if (task.parentTask) return false;

      if (assigneeId === 'unassigned' && (!task.assignees || task.assignees.length === 0)) {
        return true;
      }

      if (assigneeId.startsWith('group-')) {
        const group = assigneeGroups.find(g => g.id === assigneeId);
        if (group && task.assignees) {
          // If the group is expanded, don't show tasks on the group header
          if (expandedGroups[assigneeId]) {
            return false;
          }

          // For collapsed groups, show tasks from all group members
          return task.assignees.some(assignee => {
            const assigneeId = assignee._id ||
                              (assignee.isNonUserAssignee ?
                                `non-user-${assignee.name.replace(/\s+/g, '-').toLowerCase()}` :
                                null);
            return assigneeId && group.memberIds.includes(assigneeId);
          });
        }
        return false;
      }

      if (assigneeId.startsWith('non-user-') && task.assignees) {
        const nameFromId = assigneeId.replace('non-user-', '');
        return task.assignees.some(assignee => {
          if (assignee && (assignee.isNonUserAssignee || (!assignee._id && assignee.name))) {
            const assigneeName = assignee.name || '';
            const normalizedName = assigneeName.replace(/\s+/g, '-').toLowerCase();
            return normalizedName === nameFromId;
          }
          return false;
        });
      }

      return task.assignees &&
             task.assignees.some(assignee => assignee._id === assigneeId);
    });

    return organizeTaskHierarchy(assigneeTasks);
  }, [tasksForCurrentDay, assigneeGroups, organizeTaskHierarchy, expandedGroups]);

  // Helper function to get group names for an assignee
  const getAssigneeGroups = useCallback((assigneeId) => {
    return assigneeGroups.filter(group => 
      group.memberIds.includes(assigneeId)
    ).map(group => group.name);
  }, [assigneeGroups]);

  // Memoize display assignees with proper group expansion
  const getDisplayAssignees = useCallback(() => {
    if (!extractedAssignees || extractedAssignees.length === 0) return [];

    // Start with all individual assignees (non-grouped)
    const allGroupMembers = new Set();
    assigneeGroups.forEach(group => {
      group.memberIds.forEach(memberId => {
        allGroupMembers.add(memberId);
      });
    });

    // Create a result array that will maintain proper order
    const result = [];
    
    // Process each original assignee position to determine where to place groups
    const processedGroups = new Set();
    
    extractedAssignees.forEach((assignee) => {
      // If this assignee is not in a group, add them directly
      if (!allGroupMembers.has(assignee._id)) {
        result.push(assignee);
        return;
      }

      // Find which group this assignee belongs to
      const group = assigneeGroups.find(g => g.memberIds.includes(assignee._id));
      if (!group || processedGroups.has(group.id)) {
        return; // Skip if group already processed
      }

      // Mark this group as processed
      processedGroups.add(group.id);

      const isExpanded = expandedGroups[group.id];
      
      // Add the group
      const groupAssignee = {
        _id: group.id,
        name: group.name,
        isGroup: true,
        memberIds: group.memberIds,
        members: extractedAssignees.filter(a => group.memberIds.includes(a._id)),
        isExpanded: isExpanded
      };
      
      result.push(groupAssignee);

      // If expanded, add members immediately after
      if (isExpanded) {
        const groupMembers = extractedAssignees.filter(a => group.memberIds.includes(a._id));
        groupMembers.forEach(member => {
          result.push({
            ...member,
            groupId: group.id,
            groupName: group.name,
            isGroupMember: true
          });
        });
      }
    });

    return result;
  }, [extractedAssignees, assigneeGroups, expandedGroups]);

  const displayAssignees = useMemo(() => {
    const result = getDisplayAssignees();
    // Always show at least the Unassigned column, even if no tasks
    if (!result || result.length === 0) {
      return [{ _id: 'unassigned', name: t('calendar.dailyView.unassigned'), isSpecial: true }];
    }
    return result;
  }, [getDisplayAssignees, t]);

  // Update the column ordering to preserve group member adjacency
  const orderedDisplayAssignees = useMemo(() => {
    // Always show at least the Unassigned column, even if no tasks
    if (columnOrder.length === 0) {
      if (displayAssignees.length === 0) {
        return [{ _id: 'unassigned', name: t('calendar.dailyView.unassigned'), isSpecial: true }];
      }
      return displayAssignees;
    }

    const ordered = [];
    const remaining = [...displayAssignees];
    const processedGroupMembers = new Set();

    // Process saved order, but keep group members with their groups
    columnOrder.forEach(id => {
      const index = remaining.findIndex(a => a._id === id);
      if (index !== -1) {
        const assignee = remaining[index];
        
        // If this is a group, handle it specially
        if (assignee.isGroup) {
          // Add the group
          ordered.push(remaining.splice(index, 1)[0]);
          
          // If expanded, add its members immediately after
          if (assignee.isExpanded) {
            const memberIndices = [];
            assignee.memberIds.forEach(memberId => {
              const memberIndex = remaining.findIndex(a => 
                a._id === memberId && a.isGroupMember && a.groupId === assignee._id
              );
              if (memberIndex !== -1) {
                memberIndices.push(memberIndex);
                processedGroupMembers.add(memberId);
              }
            });
            
            // Sort indices in descending order to remove from back to front
            memberIndices.sort((a, b) => b - a);
            memberIndices.forEach(memberIndex => {
              ordered.push(remaining.splice(memberIndex, 1)[0]);
            });
          }
        } 
        // If this is a group member, only add if not already processed with its group
        else if (assignee.isGroupMember) {
          if (!processedGroupMembers.has(assignee._id)) {
            ordered.push(remaining.splice(index, 1)[0]);
          }
        }
        // Regular assignee
        else {
          ordered.push(remaining.splice(index, 1)[0]);
        }
      }
    });

    // Add any remaining assignees (new ones not in saved order)
    ordered.push(...remaining);

    return ordered;
  }, [displayAssignees, columnOrder]);

  const filteredAssignees = orderedDisplayAssignees;

  // Dynamic column sizing based on screen width and number of assignees
  const getColumnSizing = useMemo(() => {
    const assigneeCount = filteredAssignees.length;

    // Mobile-first responsive design
    if (isMobile) {
      // Use configurable mobile column width
      const mobileWidth = columnWidth.mobile;
      return {
        width: mobileWidth,
        minWidth: mobileWidth,
        maxWidth: mobileWidth
      };
    }

    // For wide screens, allow more columns to use flex sizing with configurable width
    const maxFlexColumns = windowWidth >= 1920 ? 8 : windowWidth >= 1440 ? 6 : 5;
    const desktopWidth = columnWidth.desktop;

    if (assigneeCount <= maxFlexColumns) {
      const minColumnWidth = Math.max(desktopWidth, windowWidth >= 1920 ? 220 : windowWidth >= 1440 ? 210 : 200);
      return {
        flex: 1,
        minWidth: minColumnWidth,
        maxWidth: Math.max(desktopWidth * 2, windowWidth >= 1920 ? 500 : 450)
      };
    } else {
      return {
        width: desktopWidth,
        minWidth: desktopWidth,
        maxWidth: desktopWidth
      };
    }
  }, [windowWidth, filteredAssignees.length, isMobile, columnWidth]);

  // Column drag and drop handlers
  const handleColumnMouseDown = useCallback((e, assigneeId, index) => {
    e.preventDefault();
    setIsColumnDragging(true);
    setDraggedColumnId(assigneeId);
    setDraggedColumnIndex(index);
  }, []);

  const handleColumnMouseMove = useCallback((e) => {
    if (!isColumnDragging || draggedColumnId === null) return;

    const headerElement = headerScrollRef.current;
    if (!headerElement) return;

    const rect = headerElement.getBoundingClientRect();
    const x = e.clientX - rect.left + headerElement.scrollLeft;
    
    // Calculate which column position we're over
    const timeColumnWidth = isMobile ? 60 : 80;
    const columnWidth = filteredAssignees.length <= 5
      ? (rect.width - timeColumnWidth) / filteredAssignees.length
      : (isMobile ? 100 : 150);

    const xOffsetFromTimeColumn = x - timeColumnWidth;
    let newIndex = Math.floor(xOffsetFromTimeColumn / columnWidth);
    
    // Fix: Clamp the index properly
    newIndex = Math.max(0, Math.min(newIndex, filteredAssignees.length - 1));
    
    setDropIndicatorIndex(newIndex);
  }, [isColumnDragging, draggedColumnId, filteredAssignees.length, isMobile]);

  const handleColumnMouseUp = useCallback(() => {
    if (!isColumnDragging || draggedColumnId === null || dropIndicatorIndex === -1) {
      setIsColumnDragging(false);
      setDraggedColumnId(null);
      setDraggedColumnIndex(-1);
      setDropIndicatorIndex(-1);
      return;
    }

    // Fix: Calculate the correct target index for insertion
    let targetIndex = dropIndicatorIndex;
    
    // If the drop indicator is after the dragged item, adjust for the removal
    if (dropIndicatorIndex > draggedColumnIndex) {
      targetIndex = dropIndicatorIndex - 1;
    }

    // Reorder the columns
    const newOrderedAssignees = [...filteredAssignees];
    const [draggedItem] = newOrderedAssignees.splice(draggedColumnIndex, 1);
    newOrderedAssignees.splice(targetIndex, 0, draggedItem);

    // Update the column order in state and localStorage
    const newColumnOrder = newOrderedAssignees.map(a => a._id);
    setColumnOrder(newColumnOrder);

    setIsColumnDragging(false);
    setDraggedColumnId(null);
    setDraggedColumnIndex(-1);
    setDropIndicatorIndex(-1);
  }, [isColumnDragging, draggedColumnId, draggedColumnIndex, dropIndicatorIndex, filteredAssignees]);

  // Save column order to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('dailyViewColumnOrder', JSON.stringify(columnOrder));
    } catch (error) {
      console.error('Error saving column order to localStorage:', error);
    }
  }, [columnOrder]);

  // Memoize time slots
  const timeSlots = useMemo(() => {
    const slots = [];
    const dayStart = startOfDay(dateToShow);

    for (let i = 0; i < 24; i++) {
      slots.push(addHours(dayStart, i));
    }

    return slots;
  }, [dateToShow]);

  // Get events for the current day
  const getEventsForDay = useCallback(() => {
    if (!eventsToShow || eventsToShow.length === 0) return [];

    return eventsToShow.filter(event => {
      if (!event.date) return false;
      const eventDate = new Date(event.date);
      return isSameDay(eventDate, dateToShow);
    });
  }, [eventsToShow, dateToShow]);

  const todayEvents = useMemo(() => getEventsForDay(), [getEventsForDay]);

  // Navigation handlers
  const handlePrevDay = useCallback(() => {
    const newDate = subDays(currentDate, 1);
    setCurrentDate(newDate);
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  }, [currentDate, navigate]);

  const handleNextDay = useCallback(() => {
    const newDate = addDays(currentDate, 1);
    setCurrentDate(newDate);
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  }, [currentDate, navigate]);

  const handleToday = useCallback(() => {
    const newDate = new Date();
    setCurrentDate(newDate);
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  }, [navigate]);

  // Disable navigation if viewing via shareToken
  const safeHandlePrevDay = useCallback(() => {
    if (shareToken) return;
    handlePrevDay();
  }, [shareToken, handlePrevDay]);
  const safeHandleNextDay = useCallback(() => {
    if (shareToken) return;
    handleNextDay();
  }, [shareToken, handleNextDay]);
  const safeHandleToday = useCallback(() => {
    if (shareToken) return;
    handleToday();
  }, [shareToken, handleToday]);

  const handleOpenMonthlyDialog = useCallback(() => {
    setOpenMonthlyDialog(true);
  }, []);

  const handleCloseMonthlyDialog = useCallback(() => {
    setOpenMonthlyDialog(false);
  }, []);

  // Zoom control functions
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.5, 4));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.5, 0.5));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoomLevel(1);
  }, []);

  // Column width control functions
  const handleIncreaseColumnWidth = useCallback(() => {
    const newWidth = {
      mobile: Math.min(columnWidth.mobile + 20, 300),
      desktop: Math.min(columnWidth.desktop + 30, 500)
    };
    setColumnWidth(newWidth);
    localStorage.setItem('dailyViewColumnWidth', JSON.stringify(newWidth));
  }, [columnWidth]);

  const handleDecreaseColumnWidth = useCallback(() => {
    const newWidth = {
      mobile: Math.max(columnWidth.mobile - 20, 100),
      desktop: Math.max(columnWidth.desktop - 30, 150)
    };
    setColumnWidth(newWidth);
    localStorage.setItem('dailyViewColumnWidth', JSON.stringify(newWidth));
  }, [columnWidth]);

  const handleResetColumnWidth = useCallback(() => {
    const defaultWidth = {
      mobile: 160,
      desktop: 250
    };
    setColumnWidth(defaultWidth);
    localStorage.setItem('dailyViewColumnWidth', JSON.stringify(defaultWidth));
  }, []);

  // Mobile menu handlers
  const handleMobileMenuOpen = useCallback((event) => {
    setMobileMenuAnchor(event.currentTarget);
  }, []);

  const handleMobileMenuClose = useCallback(() => {
    setMobileMenuAnchor(null);
  }, []);

  const handleDateSelect = useCallback((day) => {
    const newDate = new Date(currentDate);
    newDate.setDate(day);
    setCurrentDate(newDate);
    setOpenMonthlyDialog(false);

    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  }, [currentDate, navigate]);

  // Month navigation handlers for mini calendar
  const handlePrevMonth = useCallback(() => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  }, [currentDate]);

  const handleNextMonth = useCallback(() => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  }, [currentDate]);

  // Group management functions
  const handleOpenGroupDialog = useCallback((group = null) => {
    let originalGroup = group;
    if (group && group._id && group.isGroup) {
      originalGroup = assigneeGroups.find(g => g.id === group._id);
    }

    setSelectedGroup(originalGroup);
    setNewGroupName(originalGroup ? originalGroup.name : '');
    setGroupMemberIds(originalGroup ? originalGroup.memberIds : []);
    setOpenGroupDialog(true);
  }, [assigneeGroups]);

  const handleCloseGroupDialog = useCallback(() => {
    setOpenGroupDialog(false);
    setSelectedGroup(null);
    setNewGroupName('');
    setGroupMemberIds([]);
  }, []);

  const handleCreateOrUpdateGroup = useCallback(() => {
    if (!newGroupName.trim()) {
      setSnackbarMessage(t('calendar.dailyView.groupNameRequired', 'Group name is required'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (groupMemberIds.length < 2) {
      setSnackbarMessage(t('calendar.dailyView.minTwoMembers', 'A group must have at least 2 members'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (selectedGroup) {
      const updatedGroups = assigneeGroups.map(g =>
        g.id === selectedGroup.id
          ? { ...g, name: newGroupName, memberIds: groupMemberIds }
          : g
      );
      setAssigneeGroups(updatedGroups);
    } else {
      const newGroup = {
        id: `group-${Date.now()}`,
        name: newGroupName,
        memberIds: groupMemberIds
      };

      const updatedGroups = [...assigneeGroups, newGroup];
      setAssigneeGroups(updatedGroups);

      const newExpandedGroups = {
        ...expandedGroups,
        [newGroup.id]: false
      };

      setExpandedGroups(newExpandedGroups);
    }

    handleCloseGroupDialog();

    setSnackbarMessage(
      selectedGroup
        ? t('calendar.dailyView.groupUpdated', 'Group updated successfully')
        : t('calendar.dailyView.groupCreated', 'Group created successfully')
    );
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  }, [newGroupName, groupMemberIds, selectedGroup, assigneeGroups, expandedGroups, handleCloseGroupDialog, t]);

  const handleDeleteGroup = useCallback((groupId) => {
    const updatedGroups = assigneeGroups.filter(g => g.id !== groupId);
    setAssigneeGroups(updatedGroups);

    const newExpandedGroups = { ...expandedGroups };
    delete newExpandedGroups[groupId];
    setExpandedGroups(newExpandedGroups);

    setSnackbarMessage(t('calendar.dailyView.groupDeleted', 'Group deleted successfully'));
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  }, [assigneeGroups, expandedGroups, t]);

  const handleToggleGroupExpansion = useCallback((groupId) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  }, []);

  // Check if a specific day has any tasks
  const dayHasTasks = useCallback((day) => {
    if (!tasksToShow || tasksToShow.length === 0) return false;

    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);

    return tasksToShow.some(task => {
      if (task.deleted) return false;
      return isTaskOnDay(task, date);
    });
  }, [tasksToShow, currentDate, isTaskOnDay]);

  // Render the mini monthly calendar
  const renderMiniCalendar = useCallback(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(new Date(year, month));
    const firstDayOfMonth = getDay(new Date(year, month, 1));
    const currentDay = currentDate.getDate();
    const today = new Date();
    const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;
    const todayDate = today.getDate();

    const days = [];

    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <Grid item key={`empty-${i}`} sx={{ width: '14.28%', p: 0.5 }}>
          <Box sx={{ height: 30 }} />
        </Grid>
      );
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const isToday = isCurrentMonth && day === todayDate;
      const isSelected = day === currentDay;
      const hasTask = dayHasTasks(day);

      days.push(
        <Grid item key={day} sx={{ width: '14.28%', p: 0.5 }}>
          <Box
            onClick={() => handleDateSelect(day)}
            sx={{
              height: 30,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '50%',
              cursor: 'pointer',
              bgcolor: isSelected ? 'primary.main' : isToday ? 'primary.light' : 'transparent',
              color: isSelected ? 'primary.contrastText' : isToday ? 'primary.contrastText' : 'text.primary',
              position: 'relative',
              '&:hover': {
                bgcolor: !isSelected ? 'action.hover' : 'primary.main'
              }
            }}
          >
            <Typography variant="caption" fontWeight={isToday || isSelected ? 'bold' : 'normal'}>
              {day}
            </Typography>

            {hasTask && (
              <Box
                sx={{
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  bgcolor: isSelected ? 'primary.contrastText' : 'primary.main',
                  position: 'absolute',
                  bottom: 2
                }}
              />
            )}
          </Box>
        </Grid>
      );
    }

    return days;
  }, [currentDate, dayHasTasks, handleDateSelect]);

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Handle mouse move for dragging
  const handleMouseMove = useCallback((event) => {
    // Handle column dragging
    if (isColumnDragging) {
      handleColumnMouseMove(event);
      return;
    }

    // Check if we have a task that's preparing to drag
    if (draggedTask && draggedTask._isPreparing) {
      const deltaX = Math.abs(event.clientX - draggedTask._initialMouseX);
      const deltaY = Math.abs(event.clientY - draggedTask._initialMouseY);
      const dragThreshold = 5; // pixels

      // Only start dragging if mouse has moved beyond threshold
      if (deltaX > dragThreshold || deltaY > dragThreshold) {
        setIsDragging(true);
        setDragType(draggedTask._dragType);
        setDraggedTask(prev => ({
          ...prev,
          _isPreparing: false,
          _justDragged: true
        }));
      }
      return;
    }

    if (!isDragging || !draggedTask) return;

    const clientY = event.clientY;
    const clientX = event.clientX;

    const taskElements = document.querySelectorAll(`[data-task-id="${draggedTask._id}"]`);
    if (taskElements.length === 0) return;

    const deltaY = clientY - draggedTask._initialMouseY;

    let newTop, newHeight;

    if (dragType === 'move') {
      newTop = draggedTask._initialTop + deltaY;
      newHeight = draggedTask._initialHeight;

      taskElements.forEach(element => {
        element.style.top = `${newTop}px`;
      });
    } else if (dragType === 'resize-top') {
      newTop = draggedTask._initialTop + deltaY;
      newHeight = draggedTask._initialHeight - deltaY;

      if (newHeight >= 30) {
        taskElements.forEach(element => {
          element.style.top = `${newTop}px`;
          element.style.height = `${newHeight}px`;
        });
      } else {
        newTop = draggedTask._initialTop + (draggedTask._initialHeight - 30);
        newHeight = 30;

        taskElements.forEach(element => {
          element.style.top = `${newTop}px`;
          element.style.height = `${newHeight}px`;
        });
      }
    } else if (dragType === 'resize-bottom') {
      newTop = draggedTask._initialTop;
      newHeight = draggedTask._initialHeight + deltaY;

      if (newHeight >= 30) {
        taskElements.forEach(element => {
          element.style.height = `${newHeight}px`;
        });
      } else {
        newHeight = 30;
        taskElements.forEach(element => {
          element.style.height = `${newHeight}px`;
        });
      }
    }

    const adjustedTop = newTop / zoomLevel;
    const adjustedHeight = newHeight / zoomLevel;

    const startHour = Math.floor(adjustedTop / 60);
    const startMinute = Math.floor(adjustedTop % 60);

    const endHour = Math.floor((adjustedTop + adjustedHeight) / 60);
    const endMinute = Math.floor((adjustedTop + adjustedHeight) % 60);

    const startTimeStr = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
    const endTimeStr = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

    const durationMinutes = adjustedHeight;
    const durationHours = Math.floor(durationMinutes / 60);
    const remainingMinutes = Math.floor(durationMinutes % 60);
    const durationStr = `${durationHours}h ${remainingMinutes}m`;

    let infoText = '';
    if (dragType === 'move') {
      infoText = `${t('calendar.dailyView.dragInfo.start', { time: startTimeStr })} | ${t('calendar.dailyView.dragInfo.end', { time: endTimeStr })}`;
    } else if (dragType === 'resize-top') {
      infoText = `${t('calendar.dailyView.dragInfo.start', { time: startTimeStr })} | ${t('calendar.dailyView.dragInfo.duration', { duration: durationStr })}`;
    } else if (dragType === 'resize-bottom') {
      infoText = `${t('calendar.dailyView.dragInfo.end', { time: endTimeStr })} | ${t('calendar.dailyView.dragInfo.duration', { duration: durationStr })}`;
    }

    setDragInfo({
      text: infoText,
      x: clientX + 15,
      y: clientY + 15
    });
  }, [isDragging, draggedTask, dragType, zoomLevel, t, isColumnDragging, handleColumnMouseMove]);

  // Handle mouse up to complete dragging
  const handleMouseUp = useCallback(() => {
    // Handle column dragging
    if (isColumnDragging) {
      handleColumnMouseUp();
      return;
    }

    // If we have a task that was just preparing to drag (clicked but not dragged)
    if (draggedTask && draggedTask._isPreparing) {
      // This was just a click, not a drag - clean up and don't update the task
      setDraggedTask(null);
      setDragType(null);
      setDragInfo(null);
      return;
    }

    if (!isDragging || !draggedTask) {
      setIsDragging(false);
      setDraggedTask(null);
      setDragType(null);
      setDragInfo(null);
      return;
    }

    setJustFinishedDragging(true);
    setDragInfo(null);

    setTimeout(() => {
      setJustFinishedDragging(false);
    }, 300);

    const taskElement = document.querySelector(`[data-task-id="${draggedTask._id}"]`);
    if (!taskElement) {
      setIsDragging(false);
      setDraggedTask(null);
      setDragType(null);
      return;
    }

    const cleanTask = { ...draggedTask };
    delete cleanTask._initialMouseY;
    delete cleanTask._initialTop;
    delete cleanTask._initialHeight;
    delete cleanTask._offsetY;

    const top = parseInt(taskElement.style.top || '0', 10);
    const height = parseInt(taskElement.style.height || '60', 10);

    // Calculate the original position of the task to compare with current position
    const originalStartTime = new Date(cleanTask.startTime);
    const originalStartHour = originalStartTime.getHours();
    const originalStartMinutes = originalStartTime.getMinutes();
    const originalTop = (originalStartHour * 60 + originalStartMinutes) * zoomLevel;

    // Check if the task was actually moved by comparing positions
    const positionChanged = Math.abs(top - originalTop) > 5; // 5 pixel tolerance

    const adjustedTop = top / zoomLevel;
    const clampedTop = Math.max(0, Math.min(adjustedTop, 1439));
    const startHour = Math.floor(clampedTop / 60);
    const startMinute = Math.floor(clampedTop % 60);

    const updatedTask = { ...cleanTask };

    // Only update start time if the task was actually moved
    if (positionChanged) {
      const newStartTime = new Date(currentDate);
      newStartTime.setHours(startHour, startMinute, 0, 0);
      updatedTask.startTime = newStartTime;
    }

    // Only update task if it was actually moved or resized
    if (positionChanged) {
      // Only recalculate duration when resizing, not when just moving
      if (dragType === 'resize-top' || dragType === 'resize-bottom') {
        const adjustedHeight = height / zoomLevel;
        const totalMinutes = clampedTop + adjustedHeight;
        const clampedTotalMinutes = Math.max(clampedTop + 30, Math.min(totalMinutes, 1439));
        const endHour = Math.floor(clampedTotalMinutes / 60);
        const endMinute = Math.floor(clampedTotalMinutes % 60);

        const newEndTime = new Date(currentDate);
        newEndTime.setHours(endHour, endMinute, 0, 0);

        const durationMs = newEndTime - updatedTask.startTime;
        const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
        const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        const durationSeconds = Math.floor((durationMs % (1000 * 60)) / 1000);

        updatedTask.duration = `${durationHours.toString().padStart(2, '0')}:${durationMinutes.toString().padStart(2, '0')}:${durationSeconds.toString().padStart(2, '0')}`;

        if (updatedTask.softDeadline) {
          updatedTask.softDeadline = newEndTime;
        }
      } else if (dragType === 'move') {
        // When just moving a task, preserve the original duration and update softDeadline if it exists
        if (updatedTask.duration && updatedTask.duration !== '00:00:00') {
          // Calculate new end time based on preserved duration
          const [hours, minutes, seconds] = updatedTask.duration.split(':').map(Number);
          const newEndTime = new Date(updatedTask.startTime);
          newEndTime.setHours(newEndTime.getHours() + hours);
          newEndTime.setMinutes(newEndTime.getMinutes() + minutes);
          newEndTime.setSeconds(newEndTime.getSeconds() + seconds);

          if (updatedTask.softDeadline) {
            updatedTask.softDeadline = newEndTime;
          }
        }
      }
    }

    // Only call onTaskUpdate if the task was actually moved or resized
    if (positionChanged && typeof onTaskUpdate === 'function') {
      onTaskUpdate(updatedTask)
        .then(() => {
          setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
          setSnackbarSeverity('success');
          setSnackbarOpen(true);

          setTimeout(() => {
            if (draggedTask) {
              const cleanedTask = {...draggedTask};
              delete cleanedTask._justDragged;
            }
          }, 300);
        })
        .catch(error => {
          setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
          setSnackbarSeverity('error');
          setSnackbarOpen(true);
        });
    }

    setIsDragging(false);
    setDraggedTask(null);
    setDragType(null);
  }, [isDragging, draggedTask, dragType, zoomLevel, currentDate, onTaskUpdate, t, isColumnDragging, handleColumnMouseUp]);

  // Handle creating a new task
  const handleCreateTask = useCallback((assigneeId, hour) => {
    let assignee = null;

    if (assigneeId !== 'unassigned') {
      if (assigneeId.startsWith('group-')) {
        const group = assigneeGroups.find(g => g.id === assigneeId);
        if (group) {
          const groupAssignees = [];

          group.memberIds.forEach(memberId => {
            if (memberId.startsWith('non-user-')) {
              const nonUserAssignee = extractedAssignees.find(a => a._id === memberId);
              if (nonUserAssignee) {
                if (nonUserAssignee.originalAssignee) {
                  groupAssignees.push(nonUserAssignee.originalAssignee);
                } else {
                  groupAssignees.push({
                    name: nonUserAssignee.name,
                    isNonUserAssignee: true
                  });
                }
              }
            } else {
              const userAssignee = users && users.find(user => user._id === memberId);
              if (userAssignee) {
                groupAssignees.push(userAssignee);
              }
            }
          });

          const newTask = {
            name: '',
            taskType: 'Other',
            status: 'Not Started',
            assignees: groupAssignees,
            startTime: hour,
            softDeadline: addHours(hour, 1),
            dependencies: []
          };

          setSelectedTask(newTask);
          setOpenTaskDialog(true);
          return;
        }
      } else if (assigneeId.startsWith('non-user-')) {
        const nonUserAssignee = extractedAssignees.find(a => a._id === assigneeId);
        if (nonUserAssignee) {
          if (nonUserAssignee.originalAssignee) {
            assignee = nonUserAssignee.originalAssignee;
          } else {
            assignee = {
              name: nonUserAssignee.name,
              isNonUserAssignee: true
            };
          }
        }
      } else {
        assignee = (users && users.find(user => user._id === assigneeId)) || null;
      }
    }

    const newTask = {
      name: '',
      taskType: 'Other',
      status: 'Not Started',
      assignees: assignee ? [assignee] : [],
      startTime: hour,
      softDeadline: addHours(hour, 1),
      dependencies: []
    };

    setSelectedTask(newTask);
    setOpenTaskDialog(true);
  }, [assigneeGroups, extractedAssignees, users]);

  // Handle task form submission
  const handleTaskSubmit = useCallback((taskData) => {
    console.log('handleTaskSubmit called with data:', JSON.stringify(taskData, null, 2));

    try {
      if (selectedTask && selectedTask._id) {
        if (!taskData._id) {
          taskData._id = selectedTask._id;
        }

        if (typeof onTaskUpdate === 'function') {
          onTaskUpdate(taskData)
            .then(() => {
              setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              setOpenTaskDialog(false);
              setSelectedTask(null);
            })
            .catch(error => {
              setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
        }
      } else {
        if (typeof onTaskCreate === 'function') {
          onTaskCreate(taskData)
            .then(() => {
              setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              setOpenTaskDialog(false);
              setSelectedTask(null);
            })
            .catch(error => {
              setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
        } else if (typeof onTaskUpdate === 'function') {
          onTaskUpdate(taskData)
            .then(() => {
              setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              setOpenTaskDialog(false);
              setSelectedTask(null);
            })
            .catch(error => {
              setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
        }
      }
    } catch (error) {
      console.error('Error in handleTaskSubmit:', error);
      setSnackbarMessage(`An error occurred: ${error.message || 'Unknown error'}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      setOpenTaskDialog(false);
      setSelectedTask(null);
    }
  }, [selectedTask, onTaskUpdate, onTaskCreate, t]);

  const handleEditTask = useCallback((task) => {
    setSelectedTask(task);
    setOpenTaskDialog(true);
  }, []);

  // Mobile tooltip handlers
  const handleMobileTooltipOpen = useCallback((task) => {
    setMobileTooltipTask(task);
    setMobileTooltipOpen(true);
  }, []);

  const handleMobileTooltipClose = useCallback(() => {
    setMobileTooltipOpen(false);
    setMobileTooltipTask(null);
  }, []);

  const handleDeleteTask = useCallback(async (taskId) => {
    try {
      if (onTaskDelete) {
        await onTaskDelete(taskId);
        setSnackbarMessage(t('calendar.dailyView.taskDeleted', 'Task deleted successfully'));
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
        setOpenTaskDialog(false);
        setSelectedTask(null);
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      setSnackbarMessage(t('calendar.dailyView.failedToDeleteTask', 'Failed to delete task: {{message}}', { message: error.message || 'Unknown error' }));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  }, [onTaskDelete, t]);

  // Update current time every 5 minutes
  useEffect(() => {
    const updateTime = () => {
      console.log('Updating current time indicator');
      setCurrentTime(new Date());
    };

    updateTime();
    const intervalId = setInterval(updateTime, 300000);

    return () => clearInterval(intervalId);
  }, []);

  // Save assignee groups to localStorage when they change
  useEffect(() => {
    try {
      localStorage.setItem('dailyViewAssigneeGroups', JSON.stringify(assigneeGroups));
    } catch (error) {
      console.error('Error saving assignee groups to localStorage:', error);
    }
  }, [assigneeGroups]);

  // Save expanded groups state to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem('dailyViewExpandedGroups', JSON.stringify(expandedGroups));
    } catch (error) {
      console.error('Error saving expanded groups to localStorage:', error);
    }
  }, [expandedGroups]);

  // Calculate position for current time indicator
  const getCurrentTimePosition = useCallback(() => {
    const now = currentTime;

    if (!isSameDay(now, dateToShow)) {
      return null;
    }

    const hours = now.getHours();
    const minutes = now.getMinutes();
    const totalMinutes = (hours * 60) + minutes;

    return totalMinutes * (hourHeight / 60);
  }, [currentTime, dateToShow, hourHeight]);

  // Scroll to current time on initial render and set up scroll synchronization
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    const headerScroll = headerScrollRef.current;

    if (scrollContainer) {
      const now = new Date();
      const currentHour = now.getHours();
      const scrollPosition = currentHour * hourHeight;
      scrollContainer.scrollTop = scrollPosition;

      const syncHeaderScroll = () => {
        if (headerScroll) {
          headerScroll.scrollLeft = scrollContainer.scrollLeft;
        }
      };

      const syncContentScroll = () => {
        if (scrollContainer) {
          scrollContainer.scrollLeft = headerScroll.scrollLeft;
        }
      };

      scrollContainer.addEventListener('scroll', syncHeaderScroll);
      if (headerScroll) {
        headerScroll.addEventListener('scroll', syncContentScroll);
      }

      return () => {
        if (scrollContainer) {
          scrollContainer.removeEventListener('scroll', syncHeaderScroll);
        }
        if (headerScroll) {
          headerScroll.removeEventListener('scroll', syncContentScroll);
        }
      };
    }
  }, [hourHeight]);

  // Add keyboard event listener for left/right arrow navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft') {
        safeHandlePrevDay();
      } else if (e.key === 'ArrowRight') {
        safeHandleNextDay();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [safeHandlePrevDay, safeHandleNextDay]);

  const currentTimePosition = getCurrentTimePosition();

  // Helper to get ongoing tasks at current time
  const getOngoingAndUnfinishedTasks = useCallback(() => {
    if (!tasksForCurrentDay || currentTimePosition === null) return [];
    const now = new Date();
    return tasksForCurrentDay.filter(task => {
      if (task.deleted) return false;
      const start = task.startTime ? new Date(task.startTime) : null;
      let end = null;
      if (task.duration && task.duration !== '00:00:00') {
        const [h, m, s] = task.duration.split(':').map(Number);
        end = start ? new Date(start.getTime() + h * 3600000 + m * 60000 + s * 1000) : null;
      } else if (task.softDeadline) {
        end = new Date(task.softDeadline);
      } else if (task.hardDeadline) {
        end = new Date(task.hardDeadline);
      }
      // Ongoing: now between start and end
      const isOngoing = start && end && now >= start && now <= end && task.status !== 'Completed';
      // Unfinished but past: end < now and not completed/cancelled
      const isUnfinishedPast = end && end < now && !['Completed', 'Cancelled'].includes(task.status);
      // Filter by status
      const statusFilter = ongoingTasksStatusFilter.length === 0 || ongoingTasksStatusFilter.includes(task.status);
      return statusFilter && (isOngoing || isUnfinishedPast);
    });
  }, [tasksForCurrentDay, currentTimePosition, ongoingTasksStatusFilter]);

  const handleOngoingTasksClick = () => {
    setOngoingTasks(getOngoingAndUnfinishedTasks());
    setOngoingTasksOpen(true);
  };

  // Excel Export functionality
  const exportDailyViewToExcel = useCallback(() => {
    // Generate time slots (same as used in the UI)
    const timeSlots = [];
    for (let hour = 0; hour < 24; hour++) {
      const time = new Date(currentDate);
      time.setHours(hour, 0, 0, 0);
      timeSlots.push(time);
    }

    // Get filtered assignees (same as displayed in the UI)
    const assigneeColumns = filteredAssignees.map(assignee => {
      if (assignee._id === 'unassigned') {
        return t('calendar.dailyView.unassigned', 'Unassigned');
      } else if (assignee.isGroup) {
        return `${assignee.name} (Group)`;
      } else {
        return assignee.name || assignee.email || 'Unknown';
      }
    });

    // Create worksheet data
    const wsData = [];

    // Add header row: Time + Assignee columns
    wsData.push(['Time', ...assigneeColumns]);

    // For each time slot, create a row
    timeSlots.forEach(timeSlot => {
      const timeStr = format(timeSlot, 'h:mm a'); // e.g., "9:00 AM"
      const row = [timeStr];

      // For each assignee, find tasks that overlap with this time slot
      filteredAssignees.forEach(assignee => {
        const assigneeTasks = getTasksForAssignee(assignee._id);
        const tasksAtThisTime = [];

        assigneeTasks.forEach(task => {
          if (!task.startTime) return;

          const taskStart = new Date(task.startTime);
          let taskEnd = null;

          // Calculate task end time (same logic as in the UI)
          if (task.duration && task.duration !== '00:00:00') {
            const [hours, minutes, seconds] = task.duration.split(':').map(Number);
            taskEnd = new Date(taskStart);
            taskEnd.setHours(taskEnd.getHours() + hours);
            taskEnd.setMinutes(taskEnd.getMinutes() + minutes);
            taskEnd.setSeconds(taskEnd.getSeconds() + seconds);
          } else if (task.softDeadline) {
            taskEnd = new Date(task.softDeadline);
          } else if (task.hardDeadline) {
            taskEnd = new Date(task.hardDeadline);
          } else {
            // Default 1-hour duration
            taskEnd = new Date(taskStart.getTime() + 60 * 60 * 1000);
          }

          // Check if task overlaps with this time slot (1-hour window)
          const slotEnd = new Date(timeSlot.getTime() + 60 * 60 * 1000);

          if (taskStart < slotEnd && taskEnd > timeSlot) {
            // Task overlaps with this time slot - create detailed task info with times
            const taskInfo = task.name || 'Untitled Task';
            const startTimeStr = format(taskStart, 'h:mm a');
            const endTimeStr = format(taskEnd, 'h:mm a');

            // Create multi-line task entry
            let taskLine = `${taskInfo} (${startTimeStr} - ${endTimeStr})`;

            const taskDetails = [];
            if (task.taskType) taskDetails.push(`Type: ${task.taskType}`);
            if (task.status && task.status !== 'Not Started') taskDetails.push(`Status: ${task.status}`);
            if (task.location) taskDetails.push(`Location: ${task.location}`);

            if (taskDetails.length > 0) {
              taskLine += '\n' + taskDetails.join(' | ');
            }

            tasksAtThisTime.push(taskLine);
          }
        });

        // Join multiple tasks with double newline for clear separation
        const cellContent = tasksAtThisTime.length > 0 ? tasksAtThisTime.join('\n\n') : '';
        row.push(cellContent);
      });

      wsData.push(row);
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // Set column widths
    const colWidths = [
      { wch: 10 }, // Time column
      ...assigneeColumns.map(() => ({ wch: 25 })) // Assignee columns
    ];
    ws['!cols'] = colWidths;

    // Set row heights for better readability
    const rowHeights = wsData.map((row, index) => {
      if (index === 0) return { hpx: 20 }; // Header row

      // Calculate height based on content - find the cell with most lines
      let maxLines = 1;
      for (let i = 1; i < row.length; i++) {
        const cellContent = row[i] || '';
        const lines = cellContent.split('\n').length;
        maxLines = Math.max(maxLines, lines);
      }

      return { hpx: Math.max(20, maxLines * 15) }; // 15px per line, minimum 20px
    });
    ws['!rows'] = rowHeights;

    // Apply formatting to cells
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; R <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        if (!ws[cellAddress]) continue;

        // Initialize cell style
        if (!ws[cellAddress].s) ws[cellAddress].s = {};

        // Header row formatting
        if (R === 0) {
          ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "4472C4" } },
            alignment: { horizontal: "center", vertical: "center" },
            border: {
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } }
            }
          };
        } else {
          // Data cells formatting
          ws[cellAddress].s = {
            alignment: {
              horizontal: C === 0 ? "center" : "left",
              vertical: "top",
              wrapText: true
            },
            border: {
              top: { style: "thin", color: { rgb: "D0D0D0" } },
              bottom: { style: "thin", color: { rgb: "D0D0D0" } },
              left: { style: "thin", color: { rgb: "D0D0D0" } },
              right: { style: "thin", color: { rgb: "D0D0D0" } }
            }
          };

          // Alternate row colors for better readability
          if (R % 2 === 0) {
            ws[cellAddress].s.fill = { fgColor: { rgb: "F8F9FA" } };
          }
        }
      }
    }

    // Add worksheet to workbook
    const dateStr = format(currentDate, 'yyyy-MM-dd');
    XLSX.utils.book_append_sheet(wb, ws, `Daily View ${dateStr}`);

    // Generate and download file
    const fileName = `daily-view-timeline-${dateStr}.xlsx`;
    XLSX.writeFile(wb, fileName);
  }, [currentDate, filteredAssignees, getTasksForAssignee, t]);

  // ...existing code...
  const [isColumnMoveMode, setIsColumnMoveMode] = useState(false);
  const [columnToMoveId, setColumnToMoveId] = useState(null);

  const handleStartColumnMove = useCallback((assigneeId) => {
    setIsColumnMoveMode(true);
    setColumnToMoveId(assigneeId);
  }, []);

  const handleTargetColumnTap = useCallback((targetId) => {
    if (!isColumnMoveMode || !columnToMoveId || columnToMoveId === targetId) return;
    const fromIdx = filteredAssignees.findIndex(a => a._id === columnToMoveId);
    const toIdx = filteredAssignees.findIndex(a => a._id === targetId);
    if (fromIdx === -1 || toIdx === -1) return;
    // Move to left of target
    const newOrder = [...filteredAssignees];
    const [moving] = newOrder.splice(fromIdx, 1);
    newOrder.splice(toIdx, 0, moving);
    setColumnOrder(newOrder.map(a => a._id));
    setIsColumnMoveMode(false);
    setColumnToMoveId(null);
  }, [isColumnMoveMode, columnToMoveId, filteredAssignees, setColumnOrder]);

  const handleCancelColumnMove = useCallback(() => {
    setIsColumnMoveMode(false);
    setColumnToMoveId(null);
  }, []);
  // ...existing code...

  // Add state for rename dialog
  const [renameAssigneeDialogOpen, setRenameAssigneeDialogOpen] = useState(false);
  const [assigneeToRename, setAssigneeToRename] = useState(null);
  const [newAssigneeName, setNewAssigneeName] = useState('');
  const [renameLoading, setRenameLoading] = useState(false);
  const [renameResults, setRenameResults] = useState([]);

  // Handler to open dialog
  const handleOpenRenameAssigneeDialog = (assignee) => {
    setAssigneeToRename(assignee);
    setNewAssigneeName(assignee.name || '');
    setRenameAssigneeDialogOpen(true);
  };

  // Handler to close dialog
  const handleCloseRenameAssigneeDialog = () => {
    setRenameAssigneeDialogOpen(false);
    setAssigneeToRename(null);
    setNewAssigneeName('');
    // After dialog closes, reload calendar data so header reflects change
    if (renameResults.length > 0) {
      if (typeof reloadData === 'function') {
        reloadData();
      } else if (typeof fetchTasks === 'function') {
        fetchTasks();
      } else {
        window.location.reload();
      }
    }
  };

  // Handler to submit rename
  const handleRenameAssignee = async () => {
    if (!assigneeToRename || !newAssigneeName.trim()) return;
    setRenameLoading(true);
    setRenameResults([]);
    // Find all tasks with this assignee (non-user assignee match by _id or name)
    const affectedTasks = tasks.filter(task =>
      Array.isArray(task.assignees) &&
      task.assignees.some(a =>
        (a.isNonUserAssignee &&
          ((a._id && assigneeToRename._id && a._id === assigneeToRename._id) ||
           (a.name && assigneeToRename.name && a.name === assigneeToRename.name)))
      )
    );
    const results = [];
    for (const task of affectedTasks) {
      // Update assignee name in task
      const updatedTask = {
        ...task,
        assignees: task.assignees.map(a =>
          (a.isNonUserAssignee &&
            ((a._id && assigneeToRename._id && a._id === assigneeToRename._id) ||
             (a.name && assigneeToRename.name && a.name === assigneeToRename.name))
          )
            ? { ...a, name: newAssigneeName }
            : a
        )
      };
      try {
        // Persist to database
        await updateTask(task._id, updatedTask);
        results.push({ id: task._id, name: task.name, status: 'success' });
      } catch (err) {
        results.push({ id: task._id, name: task.name, status: 'error', error: err.message });
      }
    }
    setRenameResults(results);
    setRenameLoading(false);
    // Do NOT reload here; wait for dialog close
  };

  return (
    <Paper data-daily-view-root sx={{
      height: '100vh', // Use full viewport height
      minHeight: '600px',
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      maxWidth: 'none'
    }}>
      {/* Header with date navigation */}
      <Box sx={{
        p: { xs: 1, sm: 2 },
        borderBottom: `1px solid ${theme.palette.divider}`,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: isMobile ? 'wrap' : 'nowrap'
      }}>
        {/* Main navigation row */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          width: isMobile ? '100%' : 'auto',
          justifyContent: isMobile ? 'space-between' : 'flex-start'
        }}>
          {/* Date navigation */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton onClick={safeHandlePrevDay} size="small">
              <ArrowBackIcon />
            </IconButton>
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              sx={{
                mx: { xs: 1, sm: 2 },
                cursor: 'pointer',
                fontSize: isMobile ? '0.9rem' : undefined,
                '&:hover': { textDecoration: 'underline' }
              }}
              onClick={handleOpenMonthlyDialog}
            >
              {isMobile
                ? format(currentDate, 'MMM d, yyyy')
                : format(currentDate, 'EEEE, MMMM d, yyyy')
              }
            </Typography>
            <IconButton onClick={safeHandleNextDay} size="small">
              <ArrowForwardIcon />
            </IconButton>
            {!isMobile && (
              <Button
                startIcon={<TodayIcon />}
                onClick={safeHandleToday}
                size="small"
                sx={{ ml: 2 }}
              >
                {t('calendar.today', 'Today')}
              </Button>
            )}
          </Box>

          {/* Mobile: Menu button and Today button */}
          {isMobile && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                onClick={safeHandleToday}
                size="small"
                sx={{ mr: 1 }}
              >
                {t('calendar.today', 'Today')}
              </Button>
              <IconButton
                onClick={handleMobileMenuOpen}
                size="small"
                sx={{ ml: 1 }}
              >
                <MoreVertIcon />
              </IconButton>
            </Box>
          )}

          {/* Desktop: All controls inline */}
          {!isMobile && (
            <>
              {/* Zoom controls */}
              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Tooltip title={t('calendar.dailyView.zoomOut', 'Zoom Out')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleZoomOut} size="small" disabled={zoomLevel <= 0.5}>
                    <ZoomOutIcon />
                  </IconButton>
                </Tooltip>
                <Typography variant="body2" sx={{ mx: 1, minWidth: '45px', textAlign: 'center' }}>
                  {Math.round(zoomLevel * 100)}%
                </Typography>
                <Tooltip title={t('calendar.dailyView.zoomIn', 'Zoom In')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleZoomIn} size="small" disabled={zoomLevel >= 4}>
                    <ZoomInIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title={t('calendar.dailyView.resetZoom', 'Reset Zoom')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleResetZoom} size="small" disabled={zoomLevel === 1}>
                    <RestartAltIcon />
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Column width controls */}
              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Tooltip title={t('calendar.dailyView.decreaseColumnWidth', 'Decrease Column Width')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleDecreaseColumnWidth} size="small" disabled={isMobile ? columnWidth.mobile <= 100 : columnWidth.desktop <= 150}>
                    <UnfoldLessIcon />
                  </IconButton>
                </Tooltip>
                <Typography variant="body2" sx={{ mx: 1, minWidth: '60px', textAlign: 'center', fontSize: '0.75rem' }}>
                  {isMobile ? columnWidth.mobile : columnWidth.desktop}px
                </Typography>
                <Tooltip title={t('calendar.dailyView.increaseColumnWidth', 'Increase Column Width')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleIncreaseColumnWidth} size="small" disabled={isMobile ? columnWidth.mobile >= 300 : columnWidth.desktop >= 500}>
                    <UnfoldMoreIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title={t('calendar.dailyView.resetColumnWidth', 'Reset Column Width')} leaveDelay={0} disableInteractive>
                  <IconButton onClick={handleResetColumnWidth} size="small" disabled={columnWidth.mobile === 160 && columnWidth.desktop === 250}>
                    <RestartAltIcon />
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Group management button */}
              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
              <Tooltip title={t('calendar.dailyView.manageGroups', 'Manage Assignee Groups')} leaveDelay={0}>
                <Button
                  startIcon={<GroupWorkIcon />}
                  onClick={() => handleOpenGroupDialog()}
                  size="small"
                  color="primary"
                  variant="outlined"
                >
                  {t('calendar.dailyView.groups', 'Groups')}
                </Button>
              </Tooltip>

              {/* Excel Export button */}
              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
              <Tooltip title={t('calendar.dailyView.exportExcelTooltip', 'Export Daily View to Excel')} leaveDelay={0}>
                <Button
                  startIcon={<FileDownloadIcon />}
                  onClick={exportDailyViewToExcel}
                  size="small"
                  color="secondary"
                  variant="outlined"
                >
                  {t('calendar.dailyView.exportExcel', 'Export Excel')}
                </Button>
              </Tooltip>
              {/* Export Image button - placed next to Export Excel */}
              <Tooltip title={t('calendar.dailyView.exportImageTooltip', 'Export Daily View to Image')} leaveDelay={0}>
                <Button
                  startIcon={<FileDownloadIcon />}
                  onClick={exportDailyViewToImage}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ ml: 1 }}
                >
                  {t('calendar.dailyView.exportImage', 'Export Image')}
                </Button>
              </Tooltip>

              {/* Share Read-Only Link button */}
              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
              <Tooltip title={t('calendar.dailyView.shareReadOnlyTooltip', 'Share a read-only link to this day')} leaveDelay={0}>
                <Button
                  startIcon={<FileDownloadIcon />}
                  onClick={handleShareReadOnly}
                  size="small"
                  color="primary"
                  variant="outlined"
                  disabled={isReadOnly}
                >
                  {t('calendar.dailyView.shareReadOnly', 'Share Read-Only Link')}
                </Button>
              </Tooltip>
            </>
          )}
        </Box>

        {/* Event display - always on right for desktop, below for mobile */}
        {todayEvents.length > 0 && (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            width: isMobile ? '100%' : 'auto',
            mt: isMobile ? 1 : 0,
            justifyContent: isMobile ? 'center' : 'flex-end'
          }}>
            <EventIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant={isMobile ? "body2" : "subtitle1"}>
              {todayEvents.map(event => event.name).join(', ')}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Read-Only Banner */}
      {isReadOnly && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {t('calendar.dailyView.readOnlyBanner', 'You are viewing a read-only version of this day.')}
        </Alert>
      )}

      {/* Mobile Menu */}
      <Menu
        anchorEl={mobileMenuAnchor}
        open={Boolean(mobileMenuAnchor)}
        onClose={handleMobileMenuClose}
        slotProps={{
          paper: {
            sx: { minWidth: 200 }
          }
        }}
      >
        {/* Zoom controls */}
        <MenuItem disabled>
          <Typography variant="subtitle2" color="text.secondary">
            {t('calendar.dailyView.zoom', 'Zoom')}: {Math.round(zoomLevel * 100)}%
          </Typography>
        </MenuItem>
        <MenuItem onClick={() => { handleZoomOut(); handleMobileMenuClose(); }} disabled={zoomLevel <= 0.5}>
          <ListItemIcon>
            <ZoomOutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.zoomOut', 'Zoom Out')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleZoomIn(); handleMobileMenuClose(); }} disabled={zoomLevel >= 4}>
          <ListItemIcon>
            <ZoomInIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.zoomIn', 'Zoom In')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleResetZoom(); handleMobileMenuClose(); }} disabled={zoomLevel === 1}>
          <ListItemIcon>
            <RestartAltIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.resetZoom', 'Reset Zoom')}</ListItemText>
        </MenuItem>

        <Divider />

        {/* Column width controls */}
        <MenuItem disabled>
          <Typography variant="subtitle2" color="text.secondary">
            {t('calendar.dailyView.columnWidth', 'Column Width')}: {isMobile ? columnWidth.mobile : columnWidth.desktop}px
          </Typography>
        </MenuItem>
        <MenuItem onClick={() => { handleDecreaseColumnWidth(); handleMobileMenuClose(); }} disabled={isMobile ? columnWidth.mobile <= 100 : columnWidth.desktop <= 150}>
          <ListItemIcon>
            <UnfoldLessIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.decreaseColumnWidth', 'Decrease Width')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleIncreaseColumnWidth(); handleMobileMenuClose(); }} disabled={isMobile ? columnWidth.mobile >= 300 : columnWidth.desktop >= 500}>
          <ListItemIcon>
            <UnfoldMoreIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.increaseColumnWidth', 'Increase Width')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleResetColumnWidth(); handleMobileMenuClose(); }} disabled={columnWidth.mobile === 160 && columnWidth.desktop === 250}>
          <ListItemIcon>
            <RestartAltIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.resetColumnWidth', 'Reset Width')}</ListItemText>
        </MenuItem>

        <Divider />

        {/* Group management */}
        <MenuItem onClick={() => { handleOpenGroupDialog(); handleMobileMenuClose(); }}>
          <ListItemIcon>
            <GroupWorkIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.groups', 'Groups')}</ListItemText>
        </MenuItem>

        {/* Excel Export */}
        <MenuItem onClick={() => { exportDailyViewToExcel(); handleMobileMenuClose(); }}>
          <ListItemIcon>
            <FileDownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.exportExcel', 'Export Excel')}</ListItemText>
        </MenuItem>

        {/* Share Read-Only Link */}
        <MenuItem onClick={() => { handleShareReadOnly(); handleMobileMenuClose(); }} disabled={isReadOnly}>
          <ListItemIcon>
            <FileDownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.shareReadOnly', 'Share Read-Only Link')}</ListItemText>
        </MenuItem>

        {/* Export Image */}
        <MenuItem onClick={() => { exportDailyViewToImage(); handleMobileMenuClose(); }}>
          <ListItemIcon>
            <FileDownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('calendar.dailyView.exportImage', 'Export Image')}</ListItemText>
        </MenuItem>
      </Menu>

      {/* Sticky header for assignees */}
      <Box
        ref={headerScrollRef}
        data-daily-header-scroll-box
        sx={{
          display: 'flex',
          borderBottom: `1px solid ${theme.palette.divider}`,
          position: 'sticky',
          top: 0,
          zIndex: 100, // Increased z-index to stay above tasks
          backgroundColor: theme.palette.background.paper,
          overflowX: 'auto',
          '&::-webkit-scrollbar': {
            height: 8,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: 4,
          }
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Time column header */}
        <Box sx={{
          width: isMobile ? 60 : 80,
          minWidth: isMobile ? 60 : 80,
          p: isMobile ? 0.5 : 1,
          borderRight: `1px solid ${theme.palette.divider}`,
          textAlign: 'center',
          position: 'sticky',
          left: 0,
          zIndex: 101, // Slightly higher to ensure it stays on top
          backgroundColor: theme.palette.background.paper
        }}>
          <Typography variant={isMobile ? "caption" : "subtitle2"}>
            {isMobile ? 'Time' : t('calendar.dailyView.time', 'Time')}
          </Typography>
        </Box>

        {/* Drop indicator */}
        {isColumnDragging && dropIndicatorIndex !== -1 && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: `${(isMobile ? 60 : 80) + (dropIndicatorIndex * (getColumnSizing.flex ? (windowWidth - (isMobile ? 60 : 80)) / filteredAssignees.length : getColumnSizing.width || (isMobile ? 100 : 150)))}px`,
              width: 2,
              backgroundColor: 'primary.main',
              zIndex: 102,
              pointerEvents: 'none'
            }}
          />
        )}

        {/* Assignee column headers */}
        {filteredAssignees.map((assignee, index) => (
          <Box key={assignee._id} sx={{
            ...getColumnSizing,
            p: 1,
            borderRight: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            position: 'relative',
            opacity: isColumnDragging && draggedColumnId === assignee._id ? 0.5 : 1,
            cursor: isColumnDragging ? 'grabbing' : isColumnMoveMode ? 'pointer' : 'default',
            backgroundColor: isColumnMoveMode && assignee._id !== columnToMoveId ? 'rgba(25, 118, 210, 0.08)' : undefined,
            boxShadow: isColumnMoveMode && assignee._id === columnToMoveId ? '0 0 0 2px #1976d2' : undefined,
            // Add visual connection for group members
            ...(assignee.isGroupMember && {
              borderLeft: '3px solid rgba(25, 118, 210, 0.3)',
              backgroundColor: 'rgba(25, 118, 210, 0.05)'
            })
          }}
            onClick={isColumnMoveMode && assignee._id !== columnToMoveId ? () => handleTargetColumnTap(assignee._id) : undefined}
          >
            {/* Move button for mobile */}
            {isMobile && !isColumnMoveMode && (
              <IconButton
                size="small"
                sx={{ position: 'absolute', top: 2, right: 2, zIndex: 103, opacity: 0.7 }}
                onClick={() => handleStartColumnMove(assignee._id)}
              >
                <DragHandleIcon fontSize="small" />
              </IconButton>
            )}
            {/* Cancel button in move mode */}
            {isMobile && isColumnMoveMode && assignee._id === columnToMoveId && (
              <IconButton
                size="small"
                sx={{ position: 'absolute', top: 2, right: 2, zIndex: 104, opacity: 0.7 }}
                onClick={handleCancelColumnMove}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            )}
            {/* Drag handle - positioned in top left corner */}
            {!isMobile && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 2,
                  left: 2,
                  cursor: 'grab',
                  opacity: 0.3,
                  '&:hover': { opacity: 1 },
                  zIndex: 102,
                  bgcolor: 'rgba(255,255,255,0.7)',
                  borderRadius: '2px',
                  p: 0.25
                }}
                onMouseDown={(e) => handleColumnMouseDown(e, assignee._id, index)}
              >
                <DragHandleIcon fontSize="small" />
              </Box>
            )}

            {assignee._id === 'unassigned' ? (
              <Typography variant="subtitle2" sx={{ mt: 1 }}>
                {t('calendar.dailyView.unassigned')}
              </Typography>
            ) : assignee.isGroup ? (
              <>
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%',
                  p: 1,
                  bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.12)' : 'transparent',
                  borderRadius: 1,
                  position: 'relative'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, position: 'relative', width: '100%' }}>
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        bgcolor: theme.palette.primary.main,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <GroupWorkIcon />
                    </Avatar>

                    <Typography
                      variant={isMobile ? "caption" : "subtitle2"}
                      noWrap
                      sx={{
                        maxWidth: `calc(100% - ${isMobile ? 60 : 80}px)`,
                        fontWeight: 'bold',
                        ml: 1,
                        flex: 1,
                        fontSize: isMobile ? '0.6rem' : '0.75rem'
                      }}
                    >
                      {assignee.name}
                    </Typography>

                    <IconButton
                      size="small"
                      onClick={() => handleToggleGroupExpansion(assignee._id)}
                      sx={{
                        bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0,0,0,0.05)',
                        '&:hover': { bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.25)' : 'rgba(0,0,0,0.1)' }
                      }}
                    >
                      {expandedGroups[assignee._id] ? <KeyboardArrowUpIcon fontSize="small" /> : <KeyboardArrowRightIcon fontSize="small" />}
                    </IconButton>
                  </Box>

                  <Chip
                    size="small"
                    label={`${assignee.members.length} ${t('calendar.dailyView.members', 'members')}`}
                    color="primary"
                    variant={expandedGroups[assignee._id] ? "default" : "outlined"}
                    sx={{ mt: 0.5 }}
                  />

                  <Box sx={{ display: 'flex', mt: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenGroupDialog(assignee)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteGroup(assignee._id)}
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : assignee.isNonUserAssignee ? (
              <Box sx={{
                width: '100%',
                position: 'relative',
                pt: 1,
              }}>
                <Avatar
                  alt={assignee.name}
                  sx={{
                    width: isMobile ? 30 : 40,
                    height: isMobile ? 30 : 40,
                    mb: 1,
                    bgcolor: theme.palette.secondary.main,
                    cursor: 'pointer', // Make avatar clickable
                    boxShadow: 2,
                    '&:hover': { boxShadow: 4, opacity: 0.85 }
                  }}
                  onClick={() => handleOpenRenameAssigneeDialog(assignee)} // Open rename dialog on click
                >
                  {assignee.name ? assignee.name.charAt(0).toUpperCase() : '?'}
                </Avatar>
                <Typography
                  variant={isMobile ? "caption" : "subtitle2"}
                  noWrap
                  sx={{
                    maxWidth: '100%',
                    color: theme.palette.secondary.main,
                    fontSize: isMobile ? '0.6rem' : '0.75rem'
                  }}
                >
                  {assignee.name}
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                  {assignee.isGroupMember && (
                    <Chip
                      size="small"
                      label={`${t('calendar.dailyView.memberOf', 'Member of')} ${assignee.groupName}`}
                      color="primary"
                      variant="outlined"
                    />
                  )}
                </Box>
              </Box>
            ) : (
              <Box sx={{
                width: '100%',
                position: 'relative',
                pt: 1,
              }}>
                <Avatar
                  src={assignee.avatar}
                  alt={assignee.name}
                  sx={{
                    width: isMobile ? 30 : 40,
                    height: isMobile ? 30 : 40,
                    mb: 1,
                  }}
                >
                  {assignee.name ? assignee.name.charAt(0).toUpperCase() : '?'}
                </Avatar>
                <Typography
                  variant={isMobile ? "caption" : "subtitle2"}
                  noWrap
                  sx={{
                    maxWidth: '100%',
                    fontSize: isMobile ? '0.6rem' : '0.75rem'
                  }}
                >
                  {assignee.name}
                </Typography>
                {assignee.isGroupMember && (
                  <Chip
                    size="small"
                    label={`${t('calendar.dailyView.memberOf', 'Member of')} ${assignee.groupName}`}
                    color="primary"
                    variant="outlined"
                    sx={{ mt: 0.5 }}
                  />
                )}
              </Box>
            )}
          </Box>
        ))}
      </Box>

      {/* Scrollable content */}
      <Box
        ref={scrollContainerRef}
        data-daily-scroll-box
        sx={{
          flex: 1, // Fill remaining space
          display: 'flex',
          overflowY: 'auto',
          overflowX: 'auto',
          position: 'relative',
          width: '100%',
          minWidth: 0, // Allow shrinking
          '&::-webkit-scrollbar': {
            height: 8,
            width: 8,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.background.default,
          }
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Current time indicator */}
        {currentTimePosition !== null && (
          <>
            <Box
              sx={{
                position: 'absolute',
                top: `${currentTimePosition}px`,
                left: 0,
                width: filteredAssignees.length <= 5
                  ? '100%'
                  : `calc(80px + ${filteredAssignees.length * 150}px)`,
                height: '2px',
                backgroundColor: 'red',
                zIndex: 1101,
                pointerEvents: 'none',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  left: '80px',
                  top: '-4px',
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  backgroundColor: 'red',
                }
              }}
            />
            {/* New circle button to show ongoing tasks */}
            <IconButton
              sx={{
                position: 'absolute',
                top: `${currentTimePosition - 4}px`,
                left: 65,
                zIndex: 1102,
                backgroundColor: 'white',
                border: '2px solid red',
                width: 24,
                height: 24,
                borderRadius: '50%',
                boxShadow: 2,
                '&:hover': { backgroundColor: 'rgba(255,0,0,0.1)' }
              }}
              onClick={handleOngoingTasksClick}
              size="small"
            >
              <AccessTimeIcon sx={{ color: 'red', fontSize: 16 }} />
            </IconButton>
          </>
        )}

        {/* Drag info tooltip */}
        {dragInfo && (
          <Paper
            elevation={3}
            sx={{
              position: 'fixed',
              left: dragInfo.x,
              top: dragInfo.y,
              padding: '4px 8px',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              borderRadius: 1,
              fontSize: '0.75rem',
              zIndex: 9999,
              pointerEvents: 'none',
            }}
          >
            {dragInfo.text}
          </Paper>
        )}

        {/* Time column */}
        <Box sx={{
          width: isMobile ? 60 : 80,
          minWidth: isMobile ? 60 : 80,
          borderRight: `1px solid ${theme.palette.divider}`,
          position: 'sticky',
          left: 0,
          zIndex: 1102, // Raised z-index to always be above tasks and timeline
          backgroundColor: theme.palette.background.paper
        }}>
          {timeSlots.map((time, index) => (
            <Box
              key={index}
              sx={{
                height: hourHeight,
                borderBottom: index < timeSlots.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-end',
                position: 'relative',
                pr: 1,
                pt: 0.5
              }}
            >
              <Typography
                variant={isMobile ? "caption" : "body2"}
                sx={{
                  fontSize: isMobile ? '0.65rem' : '0.75rem',
                  color: 'text.secondary'
                }}
              >
                {isMobile ? format(time, 'ha') : format(time, 'h a')}
              </Typography>
            </Box>
          ))}
        </Box>

        {/* Assignee columns - optimized rendering */}
        {filteredAssignees.map((assignee) => (
          <AssigneeColumn
            key={assignee._id}
            assignee={assignee}
            timeSlots={timeSlots}
            hourHeight={hourHeight}
            assigneeTasks={getTasksForAssignee(assignee._id)}
            filteredAssignees={filteredAssignees}
            onCreateTask={handleCreateTask}
            theme={theme}
            t={t}
            isDragging={isDragging}
            draggedTask={draggedTask}
            dragType={dragType}
            onTaskMouseDown={safeHandleTaskMouseDown}
            onEditTask={isReadOnly ? undefined : handleEditTask}
            currentDate={dateToShow}
            zoomLevel={zoomLevel}
            getTaskColor={getTaskColor}
            columnSizing={getColumnSizing}
            isMobile={isMobile}
            onMobileTooltipOpen={isReadOnly ? undefined : handleMobileTooltipOpen}
          />
        ))}
      </Box>

      {/* Task Dialog */}
      <Dialog
        open={openTaskDialog}
        onClose={() => setOpenTaskDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedTask && selectedTask._id ? t('tasks.edit', 'Edit Task') : t('tasks.createNew', 'Create New Task')}
            <IconButton onClick={() => setOpenTaskDialog(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TaskForm
            task={selectedTask}
            onSubmit={handleTaskSubmit}
            onTaskDelete={safeOnTaskDelete}
            allTasks={tasksToShow}
            assignees={users}
            eventId={tasksToShow.length > 0 ? tasksToShow[0].event : ''}
          />
        </DialogContent>
      </Dialog>

      {/* Mini Monthly Calendar Dialog */}
      <Dialog
        open={openMonthlyDialog}
        onClose={handleCloseMonthlyDialog}
        maxWidth="xs"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton onClick={handlePrevMonth} size="small">
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" sx={{ mx: 2, minWidth: '200px', textAlign: 'center' }}>
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </Typography>
              <IconButton onClick={handleNextMonth} size="small">
                <ArrowForwardIcon />
              </IconButton>
            </Box>
            <IconButton onClick={handleCloseMonthlyDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ p: 1 }}>
            <Grid container>
              <Grid container spacing={0}>
                {dayNames.map(day => (
                  <Grid item key={day} sx={{ width: '14.28%', p: 0.5 }}>
                    <Typography variant="caption" align="center" display="block" fontWeight="bold">
                      {day}
                    </Typography>
                  </Grid>
                ))}
              </Grid>

              <Grid container spacing={0} sx={{ mt: 1 }}>
                {renderMiniCalendar()}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={safeHandleToday} startIcon={<TodayIcon />}>
            {t('calendar.today', 'Today')}
          </Button>
          <Box sx={{ flex: 1 }} />
          <Button onClick={handleCloseMonthlyDialog}>
            {t('common.close', 'Close')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Group Management Dialog */}
      <Dialog
        open={openGroupDialog}
        onClose={handleCloseGroupDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedGroup ? t('calendar.dailyView.editGroup', 'Edit Group') : t('calendar.dailyView.createGroup', 'Create Assignee Group')}
            <IconButton onClick={handleCloseGroupDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label={t('calendar.dailyView.groupName', 'Group Name')}
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              margin="normal"
              variant="outlined"
              autoFocus
            />

            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
              {t('calendar.dailyView.selectMembers', 'Select Group Members')}
            </Typography>

            <List sx={{
              maxHeight: 300,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              mt: 1
            }}>
              {extractedAssignees.filter(a => a._id !== 'unassigned' && !a.isGroup).map((assignee) => {
                const assigneeGroups = getAssigneeGroups(assignee._id);
                
                return (
                  <ListItem key={assignee._id} dense>
                    <ListItemIcon>
                      <Checkbox
                        edge="start"
                        checked={groupMemberIds.includes(assignee._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            const newMembers = [...groupMemberIds, assignee._id];
                            setGroupMemberIds(newMembers);
                          } else {
                            const newMembers = groupMemberIds.filter(id => id !== assignee._id);
                            setGroupMemberIds(newMembers);
                          }
                        }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={assignee.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {assignee.isNonUserAssignee ? t('calendar.dailyView.external', 'External') : assignee.email}
                          </Typography>
                          {assigneeGroups.length > 0 && (
                            <Box sx={{ mt: 0.5 }}>
                              {assigneeGroups.map((groupName, index) => (
                                <Chip
                                  key={index}
                                  size="small"
                                  label={groupName}
                                  color="primary"
                                  variant="outlined"
                                  sx={{ mr: 0.5, mb: 0.5 }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseGroupDialog}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleCreateOrUpdateGroup}
            variant="contained"
            color="primary"
            disabled={!newGroupName.trim() || groupMemberIds.length < 2}
          >
            {selectedGroup ? t('common.update', 'Update') : t('common.create', 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Mobile Tooltip Dialog */}
      <Dialog
        open={mobileTooltipOpen}
        onClose={handleMobileTooltipClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {mobileTooltipTask?.name || t('tasks.taskDetails', 'Task Details')}
            <IconButton onClick={handleMobileTooltipClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {mobileTooltipTask && (
            <Box sx={{ p: 1 }}>
              {/* Status */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    mr: 1,
                    bgcolor:
                      mobileTooltipTask.status === 'Completed' ? 'success.main' :
                      mobileTooltipTask.status === 'In Progress' ? 'info.main' :
                      mobileTooltipTask.status === 'Delayed' ? 'warning.main' :
                      mobileTooltipTask.status === 'Cancelled' ? 'error.main' :
                      'action.disabled'
                  }}
                />
                <Typography variant="body1" fontWeight="bold">
                  {mobileTooltipTask.status}
                </Typography>
              </Box>

              {/* Start Time */}
              {mobileTooltipTask.startTime && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AccessTimeIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.startTime', 'Start Time')}:
                  </Typography>
                  <Typography variant="body2">
                    {format(new Date(mobileTooltipTask.startTime), 'h:mm a')}
                  </Typography>
                </Box>
              )}

              {/* Duration */}
              {mobileTooltipTask.duration && mobileTooltipTask.duration !== '00:00:00' && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AccessTimeIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.duration', 'Duration')}:
                  </Typography>
                  <Typography variant="body2">
                    {(() => {
                      try {
                        const [hours, minutes] = mobileTooltipTask.duration.split(':').map(Number);
                        if (hours > 0 && minutes > 0) {
                          return `${hours}h ${minutes}m`;
                        } else if (hours > 0) {
                          return `${hours}h`;
                        } else if (minutes > 0) {
                          return `${minutes}m`;
                        }
                        return mobileTooltipTask.duration;
                      } catch (error) {
                        return mobileTooltipTask.duration;
                      }
                    })()}
                  </Typography>
                </Box>
              )}

              {/* End Time */}
              {mobileTooltipTask.startTime && mobileTooltipTask.duration && mobileTooltipTask.duration !== '00:00:00' && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AccessTimeIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.endTime', 'End Time')}:
                  </Typography>
                  <Typography variant="body2">
                    {(() => {
                      try {
                        const startTime = new Date(mobileTooltipTask.startTime);
                        const [hours, minutes, seconds] = mobileTooltipTask.duration.split(':').map(Number);
                        const endTime = new Date(startTime);
                        endTime.setHours(endTime.getHours() + hours);
                        endTime.setMinutes(endTime.getMinutes() + minutes);
                        endTime.setSeconds(endTime.getSeconds() + (seconds || 0));
                        return format(endTime, 'h:mm a');
                      } catch (error) {
                        return t('common.invalidTime', 'Invalid time');
                      }
                    })()}
                  </Typography>
                </Box>
              )}

              {/* Task Type */}
              {mobileTooltipTask.taskType && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EventIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.type', 'Type')}:
                  </Typography>
                  <Typography variant="body2">
                    {mobileTooltipTask.taskType}
                  </Typography>
                </Box>
              )}

              {/* Location */}
              {mobileTooltipTask.location && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LocationOnIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.location', 'Location')}:
                  </Typography>
                  <Typography variant="body2">
                    {mobileTooltipTask.location}
                  </Typography>
                </Box>
              )}

              {/* Assignees */}
              {mobileTooltipTask.assignees && mobileTooltipTask.assignees.length > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.assignees', 'Assignees')}:
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 0.5 }}>
                    {mobileTooltipTask.assignees.map((assignee, index) => (
                      <Chip
                        key={index}
                        size="small"
                        avatar={
                          <Avatar sx={{ bgcolor: 'primary.main', width: 20, height: 20, fontSize: '0.7rem' }}>
                            {assignee.name ? assignee.name.charAt(0).toUpperCase() :
                             assignee.firstName ? assignee.firstName.charAt(0).toUpperCase() : '?'}
                          </Avatar>
                        }
                        label={assignee.name || `${assignee.firstName || ''} ${assignee.lastName || ''}`.trim() || assignee.email}
                        variant="outlined"
                        sx={{ height: 24 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Soft Deadline */}
              {mobileTooltipTask.softDeadline && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EventIcon sx={{ mr: 1, fontSize: 16, color: 'warning.main' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.softDeadline', 'Soft Deadline')}:
                  </Typography>
                  <Typography variant="body2">
                    {format(new Date(mobileTooltipTask.softDeadline), 'MMM d, yyyy h:mm a')}
                  </Typography>
                </Box>
              )}

              {/* Hard Deadline */}
              {mobileTooltipTask.hardDeadline && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EventIcon sx={{ mr: 1, fontSize: 16, color: 'error.main' }} />
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', minWidth: '80px' }}>
                    {t('tasks.hardDeadline', 'Hard Deadline')}:
                  </Typography>
                  <Typography variant="body2">
                    {format(new Date(mobileTooltipTask.hardDeadline), 'MMM d, yyyy h:mm a')}
                  </Typography>
                </Box>
              )}

              {/* Description */}
              {mobileTooltipTask.details && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 'bold', mb: 1 }}>
                    {t('tasks.description', 'Description')}:
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                    {mobileTooltipTask.details}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleMobileTooltipClose}>
            {t('common.close', 'Close')}
          </Button>
          <Button
            onClick={() => {
              handleMobileTooltipClose();
              handleEditTask(mobileTooltipTask);
            }}
            variant="contained"
            startIcon={<EditIcon />}
          >
            {t('tasks.edit', 'Edit')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)}>
        <DialogTitle>{t('calendar.dailyView.shareReadOnlyTitle', 'Share Read-Only Link')}</DialogTitle>
        <DialogContent>
          <TextField
            value={shareDialogUrl}
            fullWidth
            InputProps={{ readOnly: true }}
            onFocus={e => e.target.select()}
            label={t('calendar.dailyView.shareReadOnlyLabel', 'Read-Only Link')}
            margin="dense"
          />
          <Typography variant="body2" sx={{ mt: 1 }}>
            {t('calendar.dailyView.shareReadOnlyHint', 'Anyone with this link can view this day (read-only).')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
              navigator.clipboard.writeText(shareDialogUrl)
                .then(() => {
                  setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopied', 'Read-only link copied to clipboard!'));
                  setSnackbarSeverity('info');
                  setSnackbarOpen(true);
                })
                .catch(() => {
                  setSnackbarMessage(t('calendar.dailyView.readOnlyLinkCopyFailed', 'Could not copy. Please copy manually: ') + shareDialogUrl);
                  setSnackbarSeverity('warning');
                  setSnackbarOpen(true);
                });
            }
          }}>
            {t('calendar.dailyView.copyLink', 'Copy Link')}
          </Button>
          <Button onClick={() => setShareDialogOpen(false)}>{t('calendar.dailyView.close', 'Close')}</Button>
        </DialogActions>
      </Dialog>

      {/* Ongoing Tasks Modal */}
      <Dialog open={ongoingTasksOpen} onClose={() => setOngoingTasksOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t('calendar.dailyView.ongoingTasksTitle', 'Ongoing & Unfinished Tasks')}</DialogTitle>
        <DialogContent>
          {/* Status Filter Chips */}
          <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {TASK_STATUSES.map(status => (
              <Chip
                key={status}
                label={status}
                color={getStatusChipColor(status)}
                variant={ongoingTasksStatusFilter.includes(status) ? 'filled' : 'outlined'}
                onClick={() => {
                  // Toggle status in filter
                  setOngoingTasksStatusFilter(prev =>
                    prev.includes(status)
                      ? prev.filter(s => s !== status)
                      : [...prev, status]
                  );
                }}
              />
            ))}
            <Chip
              label={t('common.all', 'All')}
              color={ongoingTasksStatusFilter.length === TASK_STATUSES.length ? 'primary' : 'default'}
              variant={ongoingTasksStatusFilter.length === TASK_STATUSES.length ? 'filled' : 'outlined'}
              onClick={() => {
                setOngoingTasksStatusFilter(
                  ongoingTasksStatusFilter.length === TASK_STATUSES.length ? [] : [...TASK_STATUSES]
                );
              }}
            />
          </Box>
          {/* Task List */}
          {(() => {
            // Filter tasks by selected statuses
            const filteredTasks = ongoingTasksStatusFilter.length === 0
              ? []
              : ongoingTasks.filter(task => ongoingTasksStatusFilter.includes(task.status));
            return filteredTasks.length === 0 ? (
              <Typography>{t('calendar.dailyView.noOngoingTasks', 'No ongoing or unfinished tasks at this time.')}</Typography>
            ) : (
              <List>
                {filteredTasks.map((task, idx) => {
                  const bgColor = getStatusBgColor(task.status, theme);
                  return (
                    <ListItem
                      key={task._id || idx}
                      button
                      sx={{
                        mb: 1,
                        borderRadius: 2,
                        bgcolor: bgColor,
                        boxShadow: 1,
                        '&:hover': { bgcolor: theme.palette.action.hover }
                      }}
                      onClick={() => {
                        setMobileTooltipTask(task);
                        setMobileTooltipOpen(true);
                      }}
                    >
                      <ListItemText
                        primary={<Box sx={{ fontWeight: 'bold', color: 'text.primary' }}>{task.name}</Box>}
                        secondary={
                          <Box>
                            <Chip
                              label={task.status || 'Unknown'}
                              size="small"
                              color={getStatusChipColor(task.status)}
                              sx={{ mr: 1 }}
                            />
                            {task.startTime && (
                              <span>{format(new Date(task.startTime), 'h:mm a')}</span>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  );
                })}
              </List>
            );
          })()}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOngoingTasksOpen(false)}>{t('common.close', 'Close')}</Button>
        </DialogActions>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>

      {/* Non-user assignee rename dialog */}
      <Dialog open={renameAssigneeDialogOpen} onClose={handleCloseRenameAssigneeDialog}>
        <DialogTitle>{t('calendar.dailyView.renameAssignee', 'Rename Assignee')}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label={t('calendar.dailyView.newName', 'New Name')}
            fullWidth
            value={newAssigneeName}
            onChange={e => setNewAssigneeName(e.target.value)}
            disabled={renameLoading}
          />
          {renameResults.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2">{t('calendar.dailyView.renameStatus', 'Update Status')}</Typography>
              <List>
                {renameResults.map(r => (
                  <ListItem key={r.id}>
                    <ListItemText
                      primary={r.name}
                      secondary={r.status === 'success'
                        ? t('calendar.dailyView.renameSuccess', 'Updated successfully')
                        : t('calendar.dailyView.renameError', 'Update failed') + (r.error ? `: ${r.error}` : '')}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRenameAssigneeDialog} disabled={renameLoading}>{t('common.cancel', 'Cancel')}</Button>
          <Button onClick={handleRenameAssignee} variant="contained" color="primary" disabled={!newAssigneeName.trim() || renameLoading}>{t('common.save', 'Save')}</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default DailyView;