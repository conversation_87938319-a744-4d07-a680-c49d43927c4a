import React, { useEffect, useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Paper, Alert, CircularProgress } from '@mui/material';
import DailyView from './DailyView';

const SharedDailyView = () => {
  const { token } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [events, setEvents] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [sharedDate, setSharedDate] = useState(null);

  useEffect(() => {
    setLoading(true);
    fetch(`/api/share/${token}`)
      .then(res => res.json())
      .then(data => {
        if (data.events && data.events.length > 0) {
          setEvents(data.events);
          setTasks(data.tasks || []);
          const eventDate = new Date(data.events[0].date);
          setSharedDate(eventDate);
        } else {
          setEvents([]);
          setTasks([]);
          setSharedDate(null);
        }
        setLoading(false);
      })
      .catch(err => {
        setError('Failed to load shared data.');
        setLoading(false);
      });
  }, [token]);

  if (loading) {
    return <Box sx={{ p: 4, textAlign: 'center' }}><CircularProgress /></Box>;
  }
  if (error) {
    return <Box sx={{ p: 4 }}><Alert severity="error">{error}</Alert></Box>;
  }
  if (!sharedDate) {
    return <Box sx={{ p: 4 }}><Alert severity="info">No shared data found for this day.</Alert></Box>;
  }

  // Render DailyView in read-only mode, no navbar, only shared data
  return (
    <Paper sx={{ minHeight: '100vh', boxShadow: 0, borderRadius: 0 }}>
      <DailyView
        selectedDate={sharedDate}
        events={events}
        tasks={tasks}
        users={[]}
        onTaskCreate={null}
        onTaskUpdate={null}
        onTaskDelete={null}
      />
    </Paper>
  );
};

export default SharedDailyView;
