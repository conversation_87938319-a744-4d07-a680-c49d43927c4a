import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  TextField,
  FormControlLabel,
  Checkbox,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Divider,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  InputAdornment
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import { fetchSupplies, createSupply, updateSupply, deleteSupply, addSupplyToTask, removeSupplyFromTask } from '../services/supplyService';
import config from '../config';

// Supply categories
const SUPPLY_CATEGORIES = [
  'Decoration',
  'Food',
  'Beverage',
  'Tableware',
  'Furniture',
  'Stationery',
  'Clothing',
  'Electronics',
  'Other'
];

// Supply units
const SUPPLY_UNITS = [
  'piece',
  'set',
  'box',
  'pack',
  'kg',
  'g',
  'l',
  'ml',
  'dozen',
  'pair',
  'meter',
  'cm'
];

const SupplyForm = ({ supply, onSubmit, onCancel, eventId }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: supply?.name || '',
    description: supply?.description || '',
    quantity: supply?.quantity || 1,
    unit: supply?.unit || 'piece',
    estimatedCost: supply?.estimatedCost || 0,
    actualCost: supply?.actualCost || 0,
    currency: supply?.currency || 'USD',
    isPurchased: supply?.isPurchased || false,
    purchaseDate: supply?.purchaseDate || null,
    category: supply?.category || 'Other',
    notes: supply?.notes || '',
    vendor: supply?.vendor || '',
    event: eventId,
    tasks: supply?.tasks || []
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      purchaseDate: date
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event from bubbling up to parent forms

    // Format the data before submitting
    const formattedData = {
      ...formData,
      // Convert string values to appropriate types
      quantity: parseInt(formData.quantity) || 1,
      estimatedCost: parseFloat(formData.estimatedCost) || 0,
      actualCost: parseFloat(formData.actualCost) || 0,
      isPurchased: Boolean(formData.isPurchased),
      // Ensure required fields have values
      name: formData.name || 'Unnamed Supply',
      category: formData.category || 'Other',
      currency: formData.currency || 'USD',
      unit: formData.unit || 'piece'
    };

    onSubmit(formattedData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            name="name"
            label={t('supplies.name', 'Supply Name')}
            value={formData.name}
            onChange={handleChange}
            fullWidth
            required
            margin="normal"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            name="description"
            label={t('supplies.description', 'Description')}
            value={formData.description}
            onChange={handleChange}
            fullWidth
            multiline
            rows={2}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="quantity"
            label={t('supplies.quantity', 'Quantity')}
            type="number"
            value={formData.quantity}
            onChange={handleChange}
            fullWidth
            inputProps={{ min: 0 }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="unit-label">{t('supplies.unit', 'Unit')}</InputLabel>
            <Select
              labelId="unit-label"
              name="unit"
              value={formData.unit}
              onChange={handleChange}
              label={t('supplies.unit', 'Unit')}
            >
              {SUPPLY_UNITS.map((unit) => (
                <MenuItem key={unit} value={unit}>
                  {t(`supplies.units.${unit.toLowerCase()}`, unit)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="estimatedCost"
            label={t('supplies.estimatedCost', 'Estimated Cost')}
            type="number"
            value={formData.estimatedCost}
            onChange={handleChange}
            fullWidth
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="actualCost"
            label={t('supplies.actualCost', 'Actual Cost')}
            type="number"
            value={formData.actualCost}
            onChange={handleChange}
            fullWidth
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
            }}
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="category-label">{t('supplies.category', 'Category')}</InputLabel>
            <Select
              labelId="category-label"
              name="category"
              value={formData.category}
              onChange={handleChange}
              label={t('supplies.category', 'Category')}
            >
              {SUPPLY_CATEGORIES.map((category) => (
                <MenuItem key={category} value={category}>
                  {t(`supplies.categories.${category.toLowerCase()}`, category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={6}>
          <TextField
            name="vendor"
            label={t('supplies.vendor', 'Vendor')}
            value={formData.vendor}
            onChange={handleChange}
            fullWidth
            margin="normal"
          />
        </Grid>

        <Grid item xs={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>{t('supplies.currency', 'Currency')}</InputLabel>
            <Select
              name="currency"
              value={formData.currency}
              onChange={handleChange}
              label={t('supplies.currency', 'Currency')}
            >
              {config.CURRENCIES.map(currency => (
                <MenuItem key={currency} value={currency}>{currency}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={6}>
          <FormControlLabel
            control={
              <Checkbox
                name="isPurchased"
                checked={formData.isPurchased}
                onChange={handleChange}
              />
            }
            label={t('supplies.isPurchased', 'Purchased')}
            sx={{ mt: 2 }}
          />
        </Grid>

        <Grid item xs={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={t('supplies.purchaseDate', 'Purchase Date')}
              value={formData.purchaseDate}
              onChange={handleDateChange}
              renderInput={(params) => <TextField {...params} fullWidth margin="normal" />}
              disabled={!formData.isPurchased}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12}>
          <TextField
            name="notes"
            label={t('supplies.notes', 'Notes')}
            value={formData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={2}
            margin="normal"
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button variant="outlined" onClick={onCancel}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button variant="contained" color="primary" type="submit">
            {supply ? t('common.update', 'Update') : t('common.add', 'Add')}
          </Button>
        </Grid>
      </Grid>
    </form>
  );
};

const SuppliesSection = ({ taskId, eventId, value = [], onChange, readOnly = false }) => {
  const { t } = useTranslation();
  const [supplies, setSupplies] = useState([]);
  const [taskSupplies, setTaskSupplies] = useState(value || []);
  const [openDialog, setOpenDialog] = useState(false);
  const [openNewSupplyDialog, setOpenNewSupplyDialog] = useState(false);
  const [selectedSupply, setSelectedSupply] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSupplies, setSelectedSupplies] = useState([]);

  // Update taskSupplies when value prop changes
  useEffect(() => {
    console.log('Value prop changed:', value);
    if (value && Array.isArray(value)) {
      // If value is an array of objects with _id, extract the IDs
      const supplyIds = value.map(supply =>
        typeof supply === 'string' ? supply :
        (supply && supply._id ? supply._id : supply)
      );
      console.log('Setting taskSupplies to:', supplyIds);
      setTaskSupplies(supplyIds);
    }
  }, [value]);

  // Fetch all supplies for the event
  useEffect(() => {
    const loadSupplies = async () => {
      if (!eventId) return;

      setLoading(true);
      try {
        const data = await fetchSupplies(eventId);
        setSupplies(data);
      } catch (error) {
        console.error('Error loading supplies:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSupplies();
  }, [eventId]);

  const handleOpenDialog = (supply = null, index = -1) => {
    setSelectedSupply(supply);
    setSelectedIndex(index);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedSupply(null);
    setSelectedIndex(-1);
    setSearchTerm('');
    setSelectedSupplies([]);
  };

  const handleOpenNewSupplyDialog = () => {
    setOpenNewSupplyDialog(true);
  };

  const handleCloseNewSupplyDialog = () => {
    setOpenNewSupplyDialog(false);
  };

  const handleAddExistingSupply = async (supplyId) => {
    // Check if the supply is already in the task
    if (taskSupplies.includes(supplyId)) {
      return;
    }

    try {
      // If we have a taskId (existing task), call the API to add the supply to the task
      // For new tasks, we'll just update the UI and the task will be created with the supplies
      if (taskId) {
        try {
          await addSupplyToTask(supplyId, taskId);
        } catch (error) {
          console.error('Error calling addSupplyToTask API:', error);
          // Continue with UI update even if API call fails
        }
      }

      // Add the supply to the task in the UI
      const updatedSupplies = [...taskSupplies, supplyId];
      setTaskSupplies(updatedSupplies);
      onChange(updatedSupplies);
      // Don't close dialog to allow adding multiple supplies
    } catch (error) {
      console.error('Error adding supply to task:', error);
    }
  };

  // Handle adding multiple supplies at once
  const handleAddSelectedSupplies = async () => {
    try {
      // Filter out supplies that are already in the task
      const newSupplies = selectedSupplies.filter(id => !taskSupplies.includes(id));

      if (newSupplies.length === 0) {
        handleCloseDialog();
        return;
      }

      // Add each supply to the task
      for (const supplyId of newSupplies) {
        if (taskId) {
          try {
            await addSupplyToTask(supplyId, taskId);
          } catch (error) {
            console.error(`Error adding supply ${supplyId} to task:`, error);
            // Continue with other supplies even if one fails
          }
        }
      }

      // Update the UI with all new supplies
      const updatedSupplies = [...taskSupplies, ...newSupplies];
      setTaskSupplies(updatedSupplies);
      onChange(updatedSupplies);

      // Reset selected supplies and close dialog
      setSelectedSupplies([]);
      handleCloseDialog();
    } catch (error) {
      console.error('Error adding selected supplies:', error);
    }
  };

  // Handle toggling a supply selection for multi-select
  const handleToggleSupplySelection = (supplyId) => {
    setSelectedSupplies(prev => {
      if (prev.includes(supplyId)) {
        return prev.filter(id => id !== supplyId);
      } else {
        return [...prev, supplyId];
      }
    });
  };

  const handleRemoveSupply = async (supplyId) => {
    try {
      // If we have a taskId (existing task), call the API to remove the supply from the task
      // For new tasks, we'll just update the UI
      if (taskId) {
        try {
          await removeSupplyFromTask(supplyId, taskId);
        } catch (error) {
          console.error('Error calling removeSupplyFromTask API:', error);
          // Continue with UI update even if API call fails
        }
      }

      // Remove the supply from the task in the UI
      const updatedSupplies = taskSupplies.filter(id => id !== supplyId);
      setTaskSupplies(updatedSupplies);
      onChange(updatedSupplies);
    } catch (error) {
      console.error('Error removing supply from task:', error);
    }
  };

  const handleCreateSupply = async (formData) => {
    try {
      // For new tasks (no taskId), we'll create the supply without linking it initially
      // The linking will happen when the task is saved
      if (taskId) {
        formData.tasks = [taskId];
      } else {
        // For new tasks, don't set tasks array - it will be linked after task creation
        formData.tasks = [];
      }

      // Create the supply
      const newSupply = await createSupply(formData);

      // Add the new supply to the supplies list
      setSupplies([...supplies, newSupply]);

      // Add the new supply to the task's supplies array
      // This will be included in the task data when the task is created/updated
      const updatedSupplies = [...taskSupplies, newSupply._id];
      setTaskSupplies(updatedSupplies);
      onChange(updatedSupplies);

      handleCloseNewSupplyDialog();
    } catch (error) {
      console.error('Error creating supply:', error);
    }
  };

  // Get the supply details for a given ID
  const getSupplyDetails = (supplyId) => {
    // First try to find the supply in the supplies array
    const supply = supplies.find(supply => supply._id === supplyId);

    // If the supply is not found and the supplyId is an object with _id, return it directly
    if (!supply && typeof supplyId === 'object' && supplyId._id) {
      return supplyId;
    }

    return supply;
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        {t('taskForm.supplies', 'Supplies')}
      </Typography>

      <Divider sx={{ mb: 2 }} />

      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
          disabled={readOnly}
        >
          {t('supplies.addExistingSupply', 'Add Existing Supply')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleOpenNewSupplyDialog}
          disabled={readOnly}
        >
          {t('supplies.createNewSupply', 'Create New Supply')}
        </Button>
      </Box>

      {taskSupplies.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {t('supplies.noSupplies', 'No supplies added yet')}
        </Typography>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>{t('supplies.name', 'Name')}</TableCell>
                <TableCell>{t('supplies.category', 'Category')}</TableCell>
                <TableCell align="right">{t('supplies.quantity', 'Quantity')}</TableCell>
                <TableCell align="right">{t('supplies.estimatedCost', 'Estimated Cost')}</TableCell>
                <TableCell align="right">{t('supplies.actualCost', 'Actual Cost')}</TableCell>
                <TableCell>{t('supplies.vendor', 'Vendor')}</TableCell>
                <TableCell>{t('supplies.status', 'Status')}</TableCell>
                <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {taskSupplies.map((supplyId) => {
                const supply = getSupplyDetails(supplyId);
                if (!supply) return null;

                return (
                  <TableRow key={supplyId}>
                    <TableCell>{supply.name}</TableCell>
                    <TableCell>
                      <Chip
                        label={t(`supplies.categories.${supply.category.toLowerCase()}`, supply.category)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="right">{`${supply.quantity} ${supply.unit}`}</TableCell>
                    <TableCell align="right">${supply.estimatedCost?.toLocaleString() || 0}</TableCell>
                    <TableCell align="right">
                      <Typography
                        color={supply.actualCost > supply.estimatedCost ? 'error.main' : 'inherit'}
                      >
                        ${supply.actualCost?.toLocaleString() || 0}
                      </Typography>
                    </TableCell>
                    <TableCell>{supply.vendor || '-'}</TableCell>
                    <TableCell>
                      <Chip
                        label={supply.isPurchased ? t('supplies.purchased', 'Purchased') : t('supplies.notPurchased', 'Not Purchased')}
                        size="small"
                        color={supply.isPurchased ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell align="right">
                      {!readOnly && (
                        <IconButton
                          size="small"
                          onClick={() => handleRemoveSupply(supplyId)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add Existing Supply Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        onClick={(e) => e.stopPropagation()}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('supplies.addExistingSupply', 'Add Existing Supply')}
            <IconButton onClick={handleCloseDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <Typography>{t('common.loading', 'Loading...')}</Typography>
            </Box>
          ) : supplies.length === 0 ? (
            <Typography>{t('supplies.noSuppliesAvailable', 'No supplies available')}</Typography>
          ) : (
            <>
              {/* Search field */}
              <Box sx={{ mb: 2, mt: 1 }}>
                <TextField
                  fullWidth
                  placeholder={t('common.search', 'Search supplies...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">
                        <Checkbox
                          indeterminate={selectedSupplies.length > 0 && selectedSupplies.length < supplies.filter(s => !taskSupplies.includes(s._id)).length}
                          checked={selectedSupplies.length > 0 && selectedSupplies.length === supplies.filter(s => !taskSupplies.includes(s._id)).length}
                          onChange={(e) => {
                            if (e.target.checked) {
                              // Select all supplies that aren't already in the task
                              setSelectedSupplies(supplies
                                .filter(s => !taskSupplies.includes(s._id))
                                .map(s => s._id));
                            } else {
                              setSelectedSupplies([]);
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>{t('supplies.name', 'Name')}</TableCell>
                      <TableCell>{t('supplies.category', 'Category')}</TableCell>
                      <TableCell align="right">{t('supplies.quantity', 'Quantity')}</TableCell>
                      <TableCell>{t('supplies.vendor', 'Vendor')}</TableCell>
                      <TableCell align="right">{t('common.actions', 'Actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {supplies
                      .filter(supply =>
                        supply.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        supply.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (supply.vendor && supply.vendor.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((supply) => (
                        <TableRow key={supply._id}>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedSupplies.includes(supply._id)}
                              onChange={() => handleToggleSupplySelection(supply._id)}
                              disabled={taskSupplies.includes(supply._id)}
                            />
                          </TableCell>
                          <TableCell>{supply.name}</TableCell>
                          <TableCell>
                            <Chip
                              label={t(`supplies.categories.${supply.category.toLowerCase()}`, supply.category)}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">{`${supply.quantity} ${supply.unit}`}</TableCell>
                          <TableCell>{supply.vendor || '-'}</TableCell>
                          <TableCell align="right">
                            <Button
                              size="small"
                              variant="contained"
                              onClick={() => handleAddExistingSupply(supply._id)}
                              disabled={taskSupplies.includes(supply._id)}
                            >
                              {taskSupplies.includes(supply._id)
                                ? t('supplies.alreadyAdded', 'Added')
                                : t('common.add', 'Add')}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Add selected supplies button */}
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleAddSelectedSupplies}
                  disabled={selectedSupplies.length === 0}
                >
                  {t('supplies.addSelected', 'Add Selected')} ({selectedSupplies.length})
                </Button>
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Create New Supply Dialog */}
      <Dialog
        open={openNewSupplyDialog}
        onClose={handleCloseNewSupplyDialog}
        maxWidth="md"
        fullWidth
        onClick={(e) => e.stopPropagation()}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {t('supplies.createNewSupply', 'Create New Supply')}
            <IconButton onClick={handleCloseNewSupplyDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <SupplyForm
            onSubmit={handleCreateSupply}
            onCancel={handleCloseNewSupplyDialog}
            eventId={eventId}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default SuppliesSection;
